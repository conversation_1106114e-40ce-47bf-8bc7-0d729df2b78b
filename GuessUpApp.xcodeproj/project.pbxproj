// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		C4C13C512D9454780079A547 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = C4C13C502D9454780079A547 /* FirebaseAnalytics */; };
		C4C13C532D9454780079A547 /* FirebaseAnalyticsOnDeviceConversion in Frameworks */ = {isa = PBXBuildFile; productRef = C4C13C522D9454780079A547 /* FirebaseAnalyticsOnDeviceConversion */; };
		C4C13C552D9454780079A547 /* FirebaseAnalyticsWithoutAdIdSupport in Frameworks */ = {isa = PBXBuildFile; productRef = C4C13C542D9454780079A547 /* FirebaseAnalyticsWithoutAdIdSupport */; };
		C4C13C982D9800680079A547 /* GoogleMobileAds in Frameworks */ = {isa = PBXBuildFile; productRef = C4C13C972D9800680079A547 /* GoogleMobileAds */; };
		C4C13C992D9800680079A547 /* FirebaseRemoteConfig in Frameworks */ = {isa = PBXBuildFile; productRef = C4C13C992D9800680079A547 /* FirebaseRemoteConfig */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		C41FE3F32D8D30A900B762DC /* GuessUp!.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "GuessUp!.app"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		C4C13C422D944B8C0079A547 /* Exceptions for "GuessUpApp" folder in "GuessUpApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = C41FE3F22D8D30A900B762DC /* GuessUpApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C41FE3F52D8D30A900B762DC /* GuessUpApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				C4C13C422D944B8C0079A547 /* Exceptions for "GuessUpApp" folder in "GuessUpApp" target */,
			);
			path = GuessUpApp;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C41FE3F02D8D30A900B762DC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C4C13C982D9800680079A547 /* GoogleMobileAds in Frameworks */,
				C4C13C512D9454780079A547 /* FirebaseAnalytics in Frameworks */,
				C4C13C552D9454780079A547 /* FirebaseAnalyticsWithoutAdIdSupport in Frameworks */,
				C4C13C532D9454780079A547 /* FirebaseAnalyticsOnDeviceConversion in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C41FE3EA2D8D30A900B762DC = {
			isa = PBXGroup;
			children = (
				C41FE3F52D8D30A900B762DC /* GuessUpApp */,
				C41FE3F42D8D30A900B762DC /* Products */,
			);
			sourceTree = "<group>";
		};
		C41FE3F42D8D30A900B762DC /* Products */ = {
			isa = PBXGroup;
			children = (
				C41FE3F32D8D30A900B762DC /* GuessUp!.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C41FE3F22D8D30A900B762DC /* GuessUpApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C41FE4012D8D30AD00B762DC /* Build configuration list for PBXNativeTarget "GuessUpApp" */;
			buildPhases = (
				C41FE3EF2D8D30A900B762DC /* Sources */,
				C41FE3F02D8D30A900B762DC /* Frameworks */,
				C41FE3F12D8D30A900B762DC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C41FE3F52D8D30A900B762DC /* GuessUpApp */,
			);
			name = GuessUpApp;
			packageProductDependencies = (
				C4C13C502D9454780079A547 /* FirebaseAnalytics */,
				C4C13C522D9454780079A547 /* FirebaseAnalyticsOnDeviceConversion */,
				C4C13C542D9454780079A547 /* FirebaseAnalyticsWithoutAdIdSupport */,
				C4C13C972D9800680079A547 /* GoogleMobileAds */,
				C4C13C992D9800680079A547 /* FirebaseRemoteConfig */,
			);
			productName = GuessUpApp;
			productReference = C41FE3F32D8D30A900B762DC /* GuessUp!.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C41FE3EB2D8D30A900B762DC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					C41FE3F22D8D30A900B762DC = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = C41FE3EE2D8D30A900B762DC /* Build configuration list for PBXProject "GuessUpApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				tr,
				de,
				fr,
				es,
				it,
				ru,
				ja,
				ko,
				zh,
				"zh-Hans",
				"zh-Hant",
				pt,
				ar,
				hi,
				nl,
				pl,
				sv,
				uk,
			);
			mainGroup = C41FE3EA2D8D30A900B762DC;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				C4C13C4F2D9454780079A547 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				C4C13C962D9800680079A547 /* XCRemoteSwiftPackageReference "swift-package-manager-google-mobile-ads" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = C41FE3F42D8D30A900B762DC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C41FE3F22D8D30A900B762DC /* GuessUpApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C41FE3F12D8D30A900B762DC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C41FE3EF2D8D30A900B762DC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		C41FE3FF2D8D30AD00B762DC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C41FE4002D8D30AD00B762DC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C41FE4022D8D30AD00B762DC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_ASSET_PATHS = "\"GuessUpApp/Preview Content\"";
				DEVELOPMENT_TEAM = MH8EMB5GNQ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GuessUpApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "GuessUp!";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationLandscapeRight;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.guessup.GuessUpApp;
				PRODUCT_NAME = "GuessUp!";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C41FE4032D8D30AD00B762DC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_ASSET_PATHS = "\"GuessUpApp/Preview Content\"";
				DEVELOPMENT_TEAM = MH8EMB5GNQ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GuessUpApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "GuessUp!";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationLandscapeRight;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.guessup.GuessUpApp;
				PRODUCT_NAME = "GuessUp!";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C41FE3EE2D8D30A900B762DC /* Build configuration list for PBXProject "GuessUpApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C41FE3FF2D8D30AD00B762DC /* Debug */,
				C41FE4002D8D30AD00B762DC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C41FE4012D8D30AD00B762DC /* Build configuration list for PBXNativeTarget "GuessUpApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C41FE4022D8D30AD00B762DC /* Debug */,
				C41FE4032D8D30AD00B762DC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		C4C13C4F2D9454780079A547 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.10.0;
			};
		};
		C4C13C962D9800680079A547 /* XCRemoteSwiftPackageReference "swift-package-manager-google-mobile-ads" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googleads/swift-package-manager-google-mobile-ads.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 12.2.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		C4C13C502D9454780079A547 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = C4C13C4F2D9454780079A547 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		C4C13C522D9454780079A547 /* FirebaseAnalyticsOnDeviceConversion */ = {
			isa = XCSwiftPackageProductDependency;
			package = C4C13C4F2D9454780079A547 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsOnDeviceConversion;
		};
		C4C13C542D9454780079A547 /* FirebaseAnalyticsWithoutAdIdSupport */ = {
			isa = XCSwiftPackageProductDependency;
			package = C4C13C4F2D9454780079A547 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsWithoutAdIdSupport;
		};
		C4C13C972D9800680079A547 /* GoogleMobileAds */ = {
			isa = XCSwiftPackageProductDependency;
			package = C4C13C962D9800680079A547 /* XCRemoteSwiftPackageReference "swift-package-manager-google-mobile-ads" */;
			productName = GoogleMobileAds;
		};
		C4C13C992D9800680079A547 /* FirebaseRemoteConfig */ = {
			isa = XCSwiftPackageProductDependency;
			package = C4C13C4F2D9454780079A547 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseRemoteConfig;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = C41FE3EB2D8D30A900B762DC /* Project object */;
}
