import SwiftUI
import GoogleMobileAds

struct Category: Identifiable {
    let id = UUID()
    let name: String
    let icon: String
    let color: Color
    let key: String
    var isLocked: Bool = false
}

struct CategoryView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var categories = [
        Category(name: "world_cuisine", icon: "fork.knife", color: .orange, key: "world_cuisine"),
        Category(name: "turkish_cuisine", icon: "fork.knife", color: .red, key: "turkish_cuisine"),
        Category(name: "tech_brands", icon: "laptopcomputer", color: .blue, key: "tech_brands"),
        Category(name: "netflix_shows", icon: "tv.fill", color: .red, key: "netflix_shows", isLocked: true),
        Category(name: "turkish_celebrities", icon: "person.2.fill", color: .teal, key: "turkish_celebrities", isLocked: true),
        Category(name: "world_cities", icon: "building.2.fill", color: .green, key: "world_cities"),
        Category(name: "superheroes", icon: "bolt.fill", color: .yellow, key: "superheroes", isLocked: true),
        Category(name: "footballers", icon: "sportscourt.fill", color: .purple, key: "footballers"),
        Category(name: "historical_figures", icon: "book.fill", color: .brown, key: "historical_figures", isLocked: true),
        Category(name: "world_musicians", icon: "music.note", color: .pink, key: "world_musicians", isLocked: true),
        Category(name: "world_actors", icon: "film.fill", color: .indigo, key: "world_actors", isLocked: true),
        Category(name: "bollywood_celebrities", icon: "star.fill", color: .cyan, key: "bollywood_celebrities", isLocked: true),
        Category(name: "animals", icon: "pawprint.fill", color: .mint, key: "animals")
    ]
    
    @State private var showingAdAlert = false
    @State private var selectedCategory: Category? = nil
    @State private var isLoadingAd = false
    @State private var showAdFailedAlert = false
    @State private var rewardedAd: RewardedAd?
    @State private var isAdLoaded = false
    @State private var showingPaywall = false
    
    let columns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        ZStack {
            // Dinamik arka plan gradyanı
            themeManager.backgroundGradient
                .ignoresSafeArea()
                .animation(.easeInOut(duration: 2.0), value: themeManager.currentTheme)
            
            VStack(spacing: 30) {
                // Başlık
                Text(NSLocalizedString("select_category", comment: ""))
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .padding(.top, 10)

                // Premium Deneyin Butonu (sadece subscription yoksa göster)
                if !subscriptionManager.isSubscribed {
                    Button(action: {
                        showingPaywall = true
                        // Analytics - Premium buton tıklama
                        AnalyticsManager.shared.logEvent(name: "premium_try_button_clicked", parameters: [
                            "source": "category_screen"
                        ])
                    }) {
                        HStack(spacing: 12) {
                            Image(systemName: "crown.fill")
                                .font(.title2)
                                .foregroundColor(.yellow)

                            VStack(alignment: .leading, spacing: 2) {
                                Text(NSLocalizedString("try_premium", comment: ""))
                                    .font(.headline.bold())
                                    .foregroundColor(.white)

                                Text(NSLocalizedString("unlock_all_categories", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.8))
                            }

                            Spacer()

                            Image(systemName: "arrow.right.circle.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                colors: [Color.purple, Color.pink, Color.orange],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                        .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                        .scaleEffect(1.0)
                        .animation(.easeInOut(duration: 0.1), value: showingPaywall)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 10)
                }
                
                // Kategori grid'i
                ScrollView {
                    LazyVGrid(columns: columns, spacing: 20) {
                        ForEach(categories) { category in
                            if category.isLocked {
                                Button(action: {
                                    selectedCategory = category
                                    showingAdAlert = true
                                    // Kilitli kategori tıklama event'ini Firebase'e gönder
                                    AnalyticsManager.shared.logCategorySelect(category: category.key)
                                }) {
                                    ZStack {
                                        // Arka plan gradient (kilitli - daha soluk)
                                        RoundedRectangle(cornerRadius: 24)
                                            .fill(themeManager.getCategoryGradient(for: category.key))
                                            .opacity(0.3)

                                        // Arka plan dekoratif elementler (kilitli - çok soluk)
                                        ForEach(Array(themeManager.getCategoryDecorations(for: category.key).enumerated()), id: \.offset) { index, decoration in
                                            Image(systemName: decoration)
                                                .font(.system(size: CGFloat.random(in: 15...30)))
                                                .foregroundColor(.white.opacity(0.05))
                                                .position(
                                                    x: CGFloat.random(in: 20...140),
                                                    y: CGFloat.random(in: 20...140)
                                                )
                                                .rotationEffect(.degrees(Double.random(in: -30...30)))
                                        }

                                        // Ana arka plan görseli (kilitli)
                                        Image(systemName: themeManager.getCategoryBackgroundImage(for: category.key))
                                            .font(.system(size: 70))
                                            .foregroundColor(.white.opacity(0.08))
                                            .position(x: 140, y: 80)

                                        // Kilit overlay
                                        RoundedRectangle(cornerRadius: 24)
                                            .fill(Color.black.opacity(0.4))

                                        // İçerik
                                        VStack(spacing: 12) {
                                            Spacer()

                                            Image(systemName: category.icon)
                                                .font(.system(size: 45))
                                                .foregroundColor(.white.opacity(0.7))
                                                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)

                                            Text(NSLocalizedString(category.key, comment: ""))
                                                .font(.title3.bold())
                                                .foregroundColor(.white.opacity(0.8))
                                                .multilineTextAlignment(.center)
                                                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                                                .padding(.horizontal, 8)

                                            HStack(spacing: 6) {
                                                Image(systemName: "lock.fill")
                                                    .foregroundColor(.white)
                                                    .font(.caption)
                                                Text(NSLocalizedString("unlock_with_ad", comment: ""))
                                                    .font(.caption.bold())
                                                    .foregroundColor(.white)
                                            }
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(
                                                Capsule()
                                                    .fill(Color.black.opacity(0.6))
                                                    .overlay(
                                                        Capsule()
                                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                                    )
                                            )

                                            Spacer()
                                        }
                                    }
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 160)
                                    .clipShape(RoundedRectangle(cornerRadius: 24))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 24)
                                            .stroke(Color.white.opacity(0.2), lineWidth: 2)
                                    )
                                    .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                                }
                            } else {
                                NavigationLink(destination: TeamSetupView(category: category)) {
                                    ZStack {
                                        // Arka plan gradient
                                        RoundedRectangle(cornerRadius: 24)
                                            .fill(themeManager.getCategoryGradient(for: category.key))
                                            .opacity(0.9)

                                        // Arka plan dekoratif elementler
                                        ForEach(Array(themeManager.getCategoryDecorations(for: category.key).enumerated()), id: \.offset) { index, decoration in
                                            Image(systemName: decoration)
                                                .font(.system(size: CGFloat.random(in: 20...40)))
                                                .foregroundColor(.white.opacity(0.1))
                                                .position(
                                                    x: CGFloat.random(in: 20...140),
                                                    y: CGFloat.random(in: 20...140)
                                                )
                                                .rotationEffect(.degrees(Double.random(in: -30...30)))
                                        }

                                        // Ana arka plan görseli
                                        Image(systemName: themeManager.getCategoryBackgroundImage(for: category.key))
                                            .font(.system(size: 80))
                                            .foregroundColor(.white.opacity(0.15))
                                            .position(x: 140, y: 80)

                                        // İçerik
                                        VStack(spacing: 15) {
                                            Spacer()

                                            Image(systemName: category.icon)
                                                .font(.system(size: 50))
                                                .foregroundColor(.white)
                                                .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                                            Text(NSLocalizedString(category.key, comment: ""))
                                                .font(.title3.bold())
                                                .foregroundColor(.white)
                                                .multilineTextAlignment(.center)
                                                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                                                .padding(.horizontal, 8)

                                            Spacer()
                                        }
                                    }
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 160)
                                    .clipShape(RoundedRectangle(cornerRadius: 24))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 24)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                                    )
                                    .shadow(color: .black.opacity(0.3), radius: 12, x: 0, y: 6)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .bounceIn()
                                .onTapGesture {
                                    AnimationManager.shared.playButtonTapHaptic()
                                    // Kategori seçimi ve oyun başlangıcı event'lerini Firebase'e gönder
                                    AnalyticsManager.shared.logCategorySelect(category: category.key)
                                    AnalyticsManager.shared.logGameStart(category: category.key)
                                }
                            }
                        }
                    }
                    .padding()
                }
                
                // Banner Reklamı - Sadece subscription yoksa göster
                if !subscriptionManager.isSubscribed {
                    AdBannerView(adUnitID: AdEnvironment.current.categoryBannerAdUnitID)
                        .frame(height: 50)
                        .padding(.bottom, 10)
                }
            }
            .alert(NSLocalizedString("watch_ad_to_unlock", comment: ""), isPresented: $showingAdAlert) {
                Button(NSLocalizedString("yes", comment: ""), action: {
                    if let selectedCategory = selectedCategory {
                        // Reklam izleme isteği event'ini Firebase'e gönder
                        AnalyticsManager.shared.logAdRequest(category: selectedCategory.key)
                    }
                    loadAndShowRewardedAd()
                })
                Button(NSLocalizedString("no", comment: ""), role: .cancel) { }
            }
            .alert(NSLocalizedString("ad_not_available", comment: ""), isPresented: $showAdFailedAlert) {
                Button(NSLocalizedString("ok", comment: ""), role: .cancel) { }
            }
            .sheet(isPresented: $showingPaywall) {
                PaywallCoordinator()
            }
            .overlay {
                if isLoadingAd {
                    ZStack {
                        Color.black.opacity(0.5)
                            .ignoresSafeArea()
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            Text(NSLocalizedString("ad_loading", comment: ""))
                                .foregroundColor(.white)
                                .padding(.top, 10)
                        }
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.title2.bold())
                            .foregroundColor(.white)
                    }
                }
            }
        }
        .onAppear {
            // Dikey mod için ekran yönlendirmesini ayarla
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                let geometryPreferences = UIWindowScene.GeometryPreferences.iOS(interfaceOrientations: .portrait)
                windowScene.requestGeometryUpdate(geometryPreferences) { error in
                    if error != nil {
                        print("Ekran yönlendirmesi değiştirilirken hata oluştu: \(error.localizedDescription)")
                    }
                }
            }
            
            // Açılmış kategorileri kontrol et
            loadUnlockedCategories()
            
            // Ödül reklamını önceden yükle
            preloadRewardedAd()
        }
    }
    
    // Açılmış kategorileri yükle
    private func loadUnlockedCategories() {
        // Eğer subscription varsa, tüm kategorileri aç
        if subscriptionManager.isSubscribed {
            var updatedCategories = categories
            for i in 0..<updatedCategories.count {
                updatedCategories[i].isLocked = false
            }
            categories = updatedCategories
            return
        }

        let now = Date()
        let thirtyMinutes: TimeInterval = 30 * 60 // 30 dakika saniye cinsinden

        // Create a mutable copy to update
        var updatedCategories = categories

        // Define the keys of categories that are initially locked
        let initiallyLockedKeys: Set<String> = [
            "netflix_shows",
            "turkish_celebrities",
            "superheroes",
            "historical_figures",
            "world_musicians",
            "world_actors",
            "bollywood_celebrities"
        ]

        for i in 0..<updatedCategories.count {
            let category = updatedCategories[i]
            let unlockedKey = "category_unlocked_\(category.key)"
            let timestampKey = "category_unlocked_timestamp_\(category.key)"

            // Check if this category was initially defined as locked
            if initiallyLockedKeys.contains(category.key) {
                // This category was initially locked. Check UserDefaults for unlock status.
                if UserDefaults.standard.bool(forKey: unlockedKey) {
                    if let timestamp = UserDefaults.standard.object(forKey: timestampKey) as? Date {
                        if now.timeIntervalSince(timestamp) > thirtyMinutes {
                            // 30 dakikadan fazla süre geçti, kategoriyi kilitle
                            updatedCategories[i].isLocked = true
                            UserDefaults.standard.removeObject(forKey: unlockedKey)
                            UserDefaults.standard.removeObject(forKey: timestampKey)
                            print("Kategori \(category.name) 30 dakika sonra kilitlendi.")
                        } else {
                            // Henüz 30 dakika geçmedi, kategori açık kalacak
                            updatedCategories[i].isLocked = false
                        }
                    } else {
                        // Zaman damgası yok ama kategori açık olarak işaretlenmiş, kilitle
                        updatedCategories[i].isLocked = true
                        UserDefaults.standard.removeObject(forKey: unlockedKey)
                        // Also remove timestamp if key is missing
                        UserDefaults.standard.removeObject(forKey: timestampKey)
                        print("Kategori \(category.name) zaman damgası olmadığı için kilitlendi.")
                    }
                } else {
                    // Category was initially locked but never unlocked via ad, keep it locked
                    updatedCategories[i].isLocked = true
                }
            } else {
                // This category was initially NOT locked. Ensure it stays unlocked.
                updatedCategories[i].isLocked = false
                // Clean up UserDefaults just in case
                UserDefaults.standard.removeObject(forKey: unlockedKey)
                UserDefaults.standard.removeObject(forKey: timestampKey)
            }
        }
        // Update the state variable with the new locked/unlocked states
        categories = updatedCategories
    }
    
    // Kategoriyi aç
    private func unlockCategory(_ category: Category) {
        if let index = categories.firstIndex(where: { $0.id == category.id }) {
            var updatedCategories = categories
            updatedCategories[index].isLocked = false
            categories = updatedCategories
            let key = "category_unlocked_\(category.key)"
            UserDefaults.standard.set(true, forKey: key)
            
            // Başarılı bildirim göster
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.success)
        }
    }
    
    // Ödül reklamını önceden yükle
    private func preloadRewardedAd() {
        let request = Request()
        RewardedAd.load(with: AdEnvironment.current.rewardedAdUnitID, request: request) { ad, error in
            if let error = error {
                print("Ödül reklamı yüklenemedi: \(error.localizedDescription)")
                return
            }
            rewardedAd = ad
            isAdLoaded = true
            print("Ödül reklamı başarıyla yüklendi")
        }
    }
    
    // Ödül reklamını yükle ve göster
    private func loadAndShowRewardedAd() {
        guard let selectedCategory = selectedCategory else { return }
        
        isLoadingAd = true
        
        if isAdLoaded, let rewardedAd = rewardedAd {
            // Zaten yüklenmiş reklamı göster
            rewardedAd.present(from: UIApplication.shared.windows.first?.rootViewController ?? UIViewController()) { 
                // Ödülü ver
                isLoadingAd = false
                if let selectedCategory = self.selectedCategory, let index = categories.firstIndex(where: { $0.id == selectedCategory.id }) {
                    categories[index].isLocked = false
                    let unlockedKey = "category_unlocked_\(selectedCategory.key)"
                    let timestampKey = "category_unlocked_timestamp_\(selectedCategory.key)"
                    UserDefaults.standard.set(true, forKey: unlockedKey)
                    UserDefaults.standard.set(Date(), forKey: timestampKey)

                    // Kategori açma event'ini Firebase'e gönder
                    AnalyticsManager.shared.logCategoryUnlock(category: selectedCategory.key, method: "ad_reward")

                    // Başarılı bildirim göster
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                }

                // Yeni reklam yükle
                isAdLoaded = false
                preloadRewardedAd()
            }
        } else {
            // Reklam yükle ve göster
            let request = Request()
            RewardedAd.load(with: AdEnvironment.current.rewardedAdUnitID, request: request) { ad, error in
                self.isLoadingAd = false
                
                if let error = error {
                    print("Ödül reklamı yüklenemedi: \(error.localizedDescription)")
                    showAdFailedAlert = true
                    return
                }
                
                // Reklamı göster
                ad?.present(from: UIApplication.shared.windows.first?.rootViewController ?? UIViewController()) { 
                    // Ödülü ver
                 if let selectedCategory = self.selectedCategory, let index = categories.firstIndex(where: { $0.id == selectedCategory.id }) {
                     categories[index].isLocked = false
                     let unlockedKey = "category_unlocked_\(selectedCategory.key)"
                     let timestampKey = "category_unlocked_timestamp_\(selectedCategory.key)"
                     UserDefaults.standard.set(true, forKey: unlockedKey)
                     UserDefaults.standard.set(Date(), forKey: timestampKey)

                     // Kategori açma event'ini Firebase'e gönder
                     AnalyticsManager.shared.logCategoryUnlock(category: selectedCategory.key, method: "ad_reward")

                     // Başarılı bildirim göster
                     let generator = UINotificationFeedbackGenerator()
                     generator.notificationOccurred(.success)
                 }
                }
            }
        }
    }
}

#Preview {
    CategoryView()
}
