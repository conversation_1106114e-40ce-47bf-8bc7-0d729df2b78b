//
//  AnimatedButton.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

// Gelişmiş animasyonlu buton
struct AnimatedButton: View {
    let title: String
    let action: () -> Void
    let style: ButtonAnimationStyle
    let gradient: LinearGradient
    
    @State private var isPressed = false
    @State private var scale: CGFloat = 1.0
    @State private var rotation: Double = 0
    @State private var glowOpacity: Double = 0
    @State private var sparkleOffset: CGFloat = -200
    
    enum ButtonAnimationStyle {
        case bounce
        case glow
        case sparkle
        case rotate
        case pulse
        case wave
        case outline
    }
    
    var body: some View {
        Button(action: {
            AnimationManager.shared.playButtonTapHaptic()
            performAnimation()
            action()
        }) {
            ZStack {
                // Ana buton arka planı
                if style == .outline {
                    RoundedRectangle(cornerRadius: 32)
                        .stroke(gradient, lineWidth: 3)
                        .frame(width: 250, height: 70)
                        .scaleEffect(scale)
                        .rotationEffect(.degrees(rotation))
                        .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                } else {
                    RoundedRectangle(cornerRadius: 32)
                        .fill(gradient)
                        .frame(width: 250, height: 70)
                        .scaleEffect(scale)
                        .rotationEffect(.degrees(rotation))
                        .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                
                // Glow efekti
                if style == .glow {
                    RoundedRectangle(cornerRadius: 32)
                        .stroke(Color.white, lineWidth: 2)
                        .frame(width: 250, height: 70)
                        .opacity(glowOpacity)
                        .scaleEffect(1.1)
                }
                
                // Sparkle efekti
                if style == .sparkle {
                    RoundedRectangle(cornerRadius: 32)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white.opacity(0.8),
                                    Color.clear,
                                    Color.white.opacity(0.8)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: 50, height: 70)
                        .offset(x: sparkleOffset)
                        .mask(
                            RoundedRectangle(cornerRadius: 32)
                                .frame(width: 250, height: 70)
                        )
                }
                
                // Wave efekti
                if style == .wave {
                    ForEach(0..<3) { index in
                        RoundedRectangle(cornerRadius: 32)
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                            .frame(width: 250, height: 70)
                            .scaleEffect(1.0 + CGFloat(index) * 0.1)
                            .opacity(glowOpacity / Double(index + 1))
                    }
                }
                
                // Buton metni
                Text(title)
                    .font(.title2.bold())
                    .foregroundColor(style == .outline ? .white.opacity(0.9) : .white)
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
                    .scaleEffect(scale)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .onAppear {
            startContinuousAnimation()
        }
    }
    
    private func performAnimation() {
        switch style {
        case .bounce:
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                scale = 1.2
            }
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6).delay(0.1)) {
                scale = 1.0
            }
            
        case .glow:
            withAnimation(.easeInOut(duration: 0.3)) {
                glowOpacity = 1.0
            }
            withAnimation(.easeInOut(duration: 0.3).delay(0.3)) {
                glowOpacity = 0.0
            }
            
        case .sparkle:
            sparkleOffset = -200
            withAnimation(.easeInOut(duration: 0.6)) {
                sparkleOffset = 200
            }
            
        case .rotate:
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                rotation += 360
            }
            
        case .pulse:
            withAnimation(.easeInOut(duration: 0.2)) {
                scale = 1.1
            }
            withAnimation(.easeInOut(duration: 0.2).delay(0.2)) {
                scale = 1.0
            }
            
        case .wave:
            withAnimation(.easeOut(duration: 0.8)) {
                glowOpacity = 1.0
            }
            withAnimation(.easeOut(duration: 0.8).delay(0.2)) {
                glowOpacity = 0.0
            }

        case .outline:
            withAnimation(.easeInOut(duration: 0.2)) {
                scale = 1.05
            }
            withAnimation(.easeInOut(duration: 0.2).delay(0.2)) {
                scale = 1.0
            }
        }
    }
    
    private func startContinuousAnimation() {
        switch style {
        case .pulse:
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                scale = 1.05
            }
            
        case .glow:
            withAnimation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true)) {
                glowOpacity = 0.5
            }
            
        case .wave:
            Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
                withAnimation(.easeOut(duration: 1.0)) {
                    glowOpacity = 0.8
                }
                withAnimation(.easeOut(duration: 1.0).delay(0.5)) {
                    glowOpacity = 0.0
                }
            }
            
        default:
            break
        }
    }
}

// Kategori kartı için animasyonlu buton
struct AnimatedCategoryCard: View {
    let category: Category
    let themeManager: ThemeManager
    let action: () -> Void
    
    @State private var isHovered = false
    @State private var rotationAngle: Double = 0
    @State private var scale: CGFloat = 1.0
    @State private var glowOpacity: Double = 0
    
    var body: some View {
        Button(action: {
            AnimationManager.shared.playButtonTapHaptic()
            performTapAnimation()
            action()
        }) {
            ZStack {
                // Arka plan gradient
                RoundedRectangle(cornerRadius: 24)
                    .fill(themeManager.getCategoryGradient(for: category.key))
                    .opacity(0.9)
                
                // Glow efekti
                RoundedRectangle(cornerRadius: 24)
                    .stroke(Color.white, lineWidth: 2)
                    .opacity(glowOpacity)
                    .scaleEffect(1.05)
                
                // Arka plan dekoratif elementler
                ForEach(Array(themeManager.getCategoryDecorations(for: category.key).enumerated()), id: \.offset) { index, decoration in
                    Image(systemName: decoration)
                        .font(.system(size: CGFloat.random(in: 20...40)))
                        .foregroundColor(.white.opacity(0.1))
                        .position(
                            x: CGFloat.random(in: 20...140),
                            y: CGFloat.random(in: 20...140)
                        )
                        .rotationEffect(.degrees(Double.random(in: -30...30) + rotationAngle))
                }
                
                // Ana arka plan görseli
                Image(systemName: themeManager.getCategoryBackgroundImage(for: category.key))
                    .font(.system(size: 80))
                    .foregroundColor(.white.opacity(0.15))
                    .position(x: 140, y: 80)
                    .rotationEffect(.degrees(rotationAngle * 0.5))
                
                // İçerik
                VStack(spacing: 15) {
                    Spacer()
                    
                    Image(systemName: category.icon)
                        .font(.system(size: 50))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)
                        .scaleEffect(scale)
                    
                    Text(NSLocalizedString(category.key, comment: ""))
                        .font(.title3.bold())
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                        .padding(.horizontal, 8)
                    
                    Spacer()
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 160)
            .clipShape(RoundedRectangle(cornerRadius: 24))
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
            )
            .shadow(color: .black.opacity(0.3), radius: 12, x: 0, y: 6)
            .scaleEffect(isHovered ? 1.05 : 1.0)
            .rotationEffect(.degrees(isHovered ? 2 : 0))
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                isHovered = hovering
                if hovering {
                    glowOpacity = 0.6
                } else {
                    glowOpacity = 0.0
                }
            }
        }
        .onAppear {
            startIdleAnimation()
        }
    }
    
    private func performTapAnimation() {
        // Tap animasyonu
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            scale = 1.2
            rotationAngle += 10
        }
        
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6).delay(0.1)) {
            scale = 1.0
        }
        
        // Glow efekti
        withAnimation(.easeInOut(duration: 0.5)) {
            glowOpacity = 1.0
        }
        withAnimation(.easeInOut(duration: 0.5).delay(0.3)) {
            glowOpacity = 0.0
        }
    }
    
    private func startIdleAnimation() {
        // Sürekli hafif dönme animasyonu
        withAnimation(.linear(duration: 20).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
        
        // Hafif puls efekti
        withAnimation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true)) {
            scale = 1.02
        }
    }
}

#Preview {
    VStack(spacing: 30) {
        AnimatedButton(
            title: "Bounce Button",
            action: {},
            style: .bounce,
            gradient: LinearGradient(
                gradient: Gradient(colors: [.orange, .red]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        
        AnimatedButton(
            title: "Glow Button",
            action: {},
            style: .glow,
            gradient: LinearGradient(
                gradient: Gradient(colors: [.blue, .purple]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        
        AnimatedButton(
            title: "Sparkle Button",
            action: {},
            style: .sparkle,
            gradient: LinearGradient(
                gradient: Gradient(colors: [.green, .mint]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
    }
    .padding()
    .background(Color.black)
}
