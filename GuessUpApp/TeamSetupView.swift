//
//  TeamSetupView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

struct TeamSetupView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var audioManager = AudioManager.shared
    
    let category: Category
    
    @State private var team1Name = ""
    @State private var team2Name = ""
    @State private var maxRounds = 2
    @State private var isReadyToStart = false
    
    private let roundOptions = [2, 4, 6]
    
    var body: some View {
        NavigationStack {
            ZStack {
                // Dinamik arka plan gradyanı
                themeManager.backgroundGradient
                    .ignoresSafeArea()
                    .animation(.easeInOut(duration: 2.0), value: themeManager.currentTheme)
                
                ScrollView {
                    VStack(spacing: 30) {
                        // Üst bar ve başlık
                        VStack(spacing: 20) {
                            // Geri dönüş butonu
                            <PERSON>ck {
                                Button(action: {
                                    dismiss()
                                }) {
                                    Image(systemName: "chevron.left")
                                        .font(.title2.bold())
                                        .foregroundColor(.white)
                                        .padding()
                                        .background(
                                            Circle()
                                                .fill(Color.white.opacity(0.2))
                                        )
                                }

                                Spacer()
                            }
                            .padding(.horizontal)

                            // Başlık
                            VStack(spacing: 10) {
                                Text(NSLocalizedString("team_setup", comment: ""))
                                    .font(.system(size: 36, weight: .bold, design: .rounded))
                                    .foregroundColor(.white)
                                    .multilineTextAlignment(.center)

                                Text(NSLocalizedString("team_setup_subtitle", comment: ""))
                                    .font(.title3)
                                    .foregroundColor(.white.opacity(0.8))
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .padding(.top, 10)
                        
                        // Kategori bilgisi
                        HStack {
                            Image(systemName: category.icon)
                                .font(.title2)
                                .foregroundColor(.white)
                            
                            Text(NSLocalizedString(category.name, comment: ""))
                                .font(.title2.bold())
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(Color.white.opacity(0.2))
                        )
                        
                        // Takım isimleri
                        VStack(spacing: 20) {
                            // Takım 1
                            VStack(alignment: .leading, spacing: 8) {
                                Text(NSLocalizedString("team_1_name", comment: ""))
                                    .font(.headline.bold())
                                    .foregroundColor(.white)
                                
                                TextField(NSLocalizedString("team_1_placeholder", comment: ""), text: $team1Name)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                    .font(.title3)
                                    .onChange(of: team1Name) { _ in
                                        checkReadyToStart()
                                    }
                            }
                            
                            // Takım 2
                            VStack(alignment: .leading, spacing: 8) {
                                Text(NSLocalizedString("team_2_name", comment: ""))
                                    .font(.headline.bold())
                                    .foregroundColor(.white)
                                
                                TextField(NSLocalizedString("team_2_placeholder", comment: ""), text: $team2Name)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                    .font(.title3)
                                    .onChange(of: team2Name) { _ in
                                        checkReadyToStart()
                                    }
                            }
                        }
                        .padding(.horizontal)
                        
                        // Round sayısı seçimi
                        VStack(alignment: .leading, spacing: 10) {
                            Text(NSLocalizedString("number_of_rounds", comment: ""))
                                .font(.headline.bold())
                                .foregroundColor(.white)

                            HStack(spacing: 15) {
                                ForEach(roundOptions, id: \.self) { rounds in
                                    Button(action: {
                                        maxRounds = rounds
                                        AnimationManager.shared.playButtonTapHaptic()
                                    }) {
                                        Text("\(rounds)")
                                            .font(.title2.bold())
                                            .foregroundColor(maxRounds == rounds ? .black : .white)
                                            .frame(width: 50, height: 50)
                                            .background(
                                                Circle()
                                                    .fill(maxRounds == rounds ? Color.white : Color.white.opacity(0.2))
                                            )
                                    }
                                    .scaleEffect(maxRounds == rounds ? 1.1 : 1.0)
                                    .animation(.spring(response: 0.3), value: maxRounds)
                                }
                            }

                            // Eşitlik açıklaması
                            Text(NSLocalizedString("fair_play_info", comment: ""))
                                .font(.caption)
                                .foregroundColor(.green)
                                .padding(.top, 5)
                        }
                        .padding(.horizontal)
                        
                        // Parti müziği ayarı
                        VStack(alignment: .leading, spacing: 10) {
                            Text(NSLocalizedString("party_settings", comment: ""))
                                .font(.headline.bold())
                                .foregroundColor(.white)
                            
                            HStack {
                                Image(systemName: "music.note")
                                    .foregroundColor(.white)
                                
                                Text(NSLocalizedString("party_music", comment: ""))
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Toggle("", isOn: $audioManager.isMusicEnabled)
                                    .toggleStyle(SwitchToggleStyle(tint: .green))
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 15)
                                    .fill(Color.white.opacity(0.2))
                            )
                        }
                        .padding(.horizontal)
                        
                        // Başlat butonu
                        NavigationLink(destination: GameView(
                            category: category,
                            team1Name: team1Name.isEmpty ? NSLocalizedString("team_1_default", comment: "") : team1Name,
                            team2Name: team2Name.isEmpty ? NSLocalizedString("team_2_default", comment: "") : team2Name,
                            maxRounds: maxRounds
                        )) {
                            Text(NSLocalizedString("start_party", comment: ""))
                                .font(.title2.bold())
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 60)
                                .background(
                                    RoundedRectangle(cornerRadius: 30)
                                        .fill(
                                            isReadyToStart ?
                                            LinearGradient(
                                                gradient: Gradient(colors: [.green, .blue]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            ) :
                                            LinearGradient(
                                                gradient: Gradient(colors: [.gray, .gray]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                )
                        }
                        .disabled(!isReadyToStart)
                        .padding(.horizontal)
                        .padding(.top, 20)
                        .simultaneousGesture(TapGesture().onEnded {
                            if isReadyToStart {
                                startGame()
                            }
                        })
                        
                        Spacer(minLength: 50)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // Varsayılan takım isimleri
            team1Name = NSLocalizedString("team_1_default", comment: "")
            team2Name = NSLocalizedString("team_2_default", comment: "")
            checkReadyToStart()
        }
    }
    
    private func checkReadyToStart() {
        isReadyToStart = !team1Name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                        !team2Name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private func startGame() {
        AnimationManager.shared.playButtonTapHaptic()
        audioManager.playGameStartSound()

        // Analytics event
        AnalyticsManager.shared.logTeamGameStart(
            category: category.key,
            team1Name: team1Name.isEmpty ? NSLocalizedString("team_1_default", comment: "") : team1Name,
            team2Name: team2Name.isEmpty ? NSLocalizedString("team_2_default", comment: "") : team2Name,
            rounds: maxRounds
        )
    }
}

#Preview {
    TeamSetupView(category: Category(name: "world_cuisine", icon: "fork.knife", color: .orange, key: "world_cuisine"))
}
