//
//  PaywallTestView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

struct PaywallTestView: View {
    @StateObject private var variantManager = PaywallVariantManager.shared
    @State private var showingPaywall = false
    @State private var selectedVariant: PaywallVariant = .original
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Paywall A/B Test")
                    .font(.largeTitle.bold())
                    .padding()
                
                VStack(alignment: .leading, spacing: 16) {
                    Text("Current Variant: \(variantManager.currentVariant.displayName)")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    Text("Test Status: \(variantManager.isTestExpired() ? "Expired" : "Active")")
                        .font(.subheadline)
                        .foregroundColor(variantManager.isTestExpired() ? .red : .green)
                    
                    Divider()
                    
                    Text("Manual Test")
                        .font(.headline)
                    
                    Picker("Select Variant", selection: $selectedVariant) {
                        ForEach(PaywallVariant.allCases, id: \.self) { variant in
                            Text(variant.displayName).tag(variant)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Button("Set Variant & Show Paywall") {
                        variantManager.setVariant(selectedVariant)
                        showingPaywall = true
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Divider()
                    
                    Text("Remote Config Values")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Show Social Proof: \(variantManager.showSocialProof ? "Yes" : "No")")
                        Text("Trial Duration: \(variantManager.trialDurationDays) days")
                        Text("Discount: \(variantManager.discountPercentage)%")
                        Text("Title: \(variantManager.paywallTitle)")
                        Text("Subtitle: \(variantManager.paywallSubtitle)")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                    
                    Button("Refresh Remote Config") {
                        Task {
                            await variantManager.refreshRemoteConfig()
                        }
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
                .padding()
                
                Spacer()
                
                VStack(spacing: 12) {
                    Text("Analytics Events")
                        .font(.headline)
                    
                    Button("Log Paywall Shown") {
                        AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Log Purchase Attempt") {
                        AnalyticsManager.shared.logPaywallPurchaseAttempt(variant: variantManager.getCurrentVariantForAnalytics())
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Log Purchase Success") {
                        AnalyticsManager.shared.logPaywallPurchaseSuccess(
                            variant: variantManager.getCurrentVariantForAnalytics(),
                            productId: "test_product",
                            revenue: 4.99
                        )
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            }
            .navigationTitle("Paywall Testing")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingPaywall) {
            PaywallCoordinator()
        }
    }
}

#Preview {
    PaywallTestView()
}
