//
//  RatingManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import Foundation
import StoreKit
import SwiftUI

class RatingManager: ObservableObject {
    static let shared = RatingManager()
    
    private init() {}
    
    // MARK: - Constants
    private let gamesRequiredForRating = 2
    private let minimumDaysBetweenRequests = 30
    
    // MARK: - UserDefaults Keys
    private let gamesPlayedKey = "games_played_count"
    private let lastRatingRequestDateKey = "last_rating_request_date"
    private let hasRequestedRatingKey = "has_requested_rating"
    
    // MARK: - Properties
    @AppStorage("games_played_count") private var gamesPlayedCount = 0
    @AppStorage("has_requested_rating") private var hasRequestedRating = false
    
    // MARK: - Public Methods
    
    /// Oyun tamamlandığında çağrılır
    func gameCompleted() {
        gamesPlayedCount += 1
        print("Oyun tamamlandı. Toplam oyun sayısı: \(gamesPlayedCount)")
        
        // 2 oyun tamamlandıysa ve daha önce değerlendirme istenmemişse
        if shouldRequestRating() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.requestRating()
            }
        }
    }
    
    /// Değerlendirme isteği gösterilmeli mi kontrol eder
    private func shouldRequestRating() -> Bool {
        // Daha önce değerlendirme istenmişse false döndür
        if hasRequestedRating {
            return false
        }
        
        // Minimum oyun sayısına ulaşılmış mı kontrol et
        if gamesPlayedCount < gamesRequiredForRating {
            return false
        }
        
        // Son değerlendirme isteğinden bu yana yeterli zaman geçmiş mi kontrol et
        if let lastRequestDate = UserDefaults.standard.object(forKey: lastRatingRequestDateKey) as? Date {
            let daysSinceLastRequest = Calendar.current.dateComponents([.day], from: lastRequestDate, to: Date()).day ?? 0
            if daysSinceLastRequest < minimumDaysBetweenRequests {
                return false
            }
        }
        
        return true
    }
    
    /// App Store değerlendirme isteği gösterir
    private func requestRating() {
        print("App Store değerlendirme isteği gösteriliyor...")
        
        // Analytics event'i gönder
        AnalyticsManager.shared.logRatingRequest()
        
        // StoreKit ile değerlendirme isteği göster
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            SKStoreReviewController.requestReview(in: windowScene)
        }
        
        // İstek tarihini kaydet
        UserDefaults.standard.set(Date(), forKey: lastRatingRequestDateKey)
        hasRequestedRating = true
        
        print("Değerlendirme isteği gösterildi ve kaydedildi.")
    }
    
    /// Manuel olarak değerlendirme isteği gösterir (ayarlar menüsü için)
    func requestRatingManually() {
        print("Manuel değerlendirme isteği...")
        AnalyticsManager.shared.logRatingRequest()
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            SKStoreReviewController.requestReview(in: windowScene)
        }
    }
    
    /// App Store'da uygulamayı açar
    func openAppStore() {
        // Gerçek App Store ID'si ile değiştirilmeli
        let appStoreURL = "https://apps.apple.com/app/id6738398035" // GuessUp App Store ID'si
        
        if let url = URL(string: appStoreURL) {
            UIApplication.shared.open(url)
        }
    }
    
    /// Değerlendirme durumunu sıfırlar (test amaçlı)
    func resetRatingStatus() {
        gamesPlayedCount = 0
        hasRequestedRating = false
        UserDefaults.standard.removeObject(forKey: lastRatingRequestDateKey)
        print("Değerlendirme durumu sıfırlandı.")
    }
    
    // MARK: - Computed Properties
    
    /// Şu ana kadar oynanan oyun sayısı
    var totalGamesPlayed: Int {
        return gamesPlayedCount
    }
    
    /// Değerlendirme için gereken kalan oyun sayısı
    var gamesRemainingForRating: Int {
        return max(0, gamesRequiredForRating - gamesPlayedCount)
    }
    
    /// Değerlendirme isteği daha önce gösterilmiş mi
    var hasShownRatingRequest: Bool {
        return hasRequestedRating
    }
}
