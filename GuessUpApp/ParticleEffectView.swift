//
//  ParticleEffectView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

// Parçacık efekt görünümü
struct ParticleEffectView: View {
    @StateObject private var animationManager = AnimationManager.shared
    @State private var particles: [Particle] = []
    @State private var timer: Timer?
    
    let effectType: EffectType
    let triggerPosition: CGPoint
    let isActive: Bool
    
    enum EffectType {
        case confetti
        case stars
        case hearts
        case sparkles
    }
    
    var body: some View {
        ZStack {
            ForEach(Array(particles.enumerated()), id: \.offset) { index, particle in
                ParticleView(particle: particle)
            }
        }
        .onChange(of: isActive) { active in
            if active {
                startEffect()
            } else {
                stopEffect()
            }
        }
        .onDisappear {
            stopEffect()
        }
    }
    
    private func startEffect() {
        // Mevcut parçacıkları temizle
        particles.removeAll()
        
        // Yeni parçacıklar oluştur
        createParticles()
        
        // Güncelleme timer'ını başlat
        timer = Timer.scheduledTimer(withTimeInterval: 1/60, repeats: true) { _ in
            updateParticles()
        }
        
        // 3 saniye sonra durdur
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            stopEffect()
        }
    }
    
    private func stopEffect() {
        timer?.invalidate()
        timer = nil
        
        // Parçacıkları yavaşça kaybet
        withAnimation(.easeOut(duration: 1.0)) {
            particles.removeAll()
        }
    }
    
    private func createParticles() {
        let particleCount = getParticleCount()
        
        for _ in 0..<particleCount {
            let particle = createParticle()
            particles.append(particle)
        }
    }
    
    private func getParticleCount() -> Int {
        switch effectType {
        case .confetti: return 25
        case .stars: return 15
        case .hearts: return 12
        case .sparkles: return 20
        }
    }
    
    private func createParticle() -> Particle {
        switch effectType {
        case .confetti:
            return Particle(
                position: triggerPosition,
                velocity: CGPoint(
                    x: Double.random(in: -250...250),
                    y: Double.random(in: -400...(-150))
                ),
                color: getRandomColor(),
                size: Double.random(in: 6...14),
                life: Double.random(in: 2.0...3.5),
                gravity: 600,
                shape: .circle
            )
            
        case .stars:
            return Particle(
                position: triggerPosition,
                velocity: CGPoint(
                    x: Double.random(in: -200...200),
                    y: Double.random(in: -300...300)
                ),
                color: .yellow,
                size: Double.random(in: 8...18),
                life: Double.random(in: 1.5...2.5),
                gravity: 0,
                shape: .star
            )
            
        case .hearts:
            return Particle(
                position: triggerPosition,
                velocity: CGPoint(
                    x: Double.random(in: -150...150),
                    y: Double.random(in: -200...(-50))
                ),
                color: .pink,
                size: Double.random(in: 10...20),
                life: Double.random(in: 2.0...3.0),
                gravity: 200,
                shape: .heart
            )
            
        case .sparkles:
            return Particle(
                position: CGPoint(
                    x: triggerPosition.x + Double.random(in: -50...50),
                    y: triggerPosition.y + Double.random(in: -50...50)
                ),
                velocity: CGPoint(
                    x: Double.random(in: -100...100),
                    y: Double.random(in: -100...100)
                ),
                color: getSparkleColor(),
                size: Double.random(in: 4...10),
                life: Double.random(in: 1.0...2.0),
                gravity: 0,
                shape: .star
            )
        }
    }
    
    private func getRandomColor() -> Color {
        let colors: [Color] = [.red, .orange, .yellow, .green, .blue, .purple, .pink, .cyan, .mint]
        return colors.randomElement() ?? .blue
    }
    
    private func getSparkleColor() -> Color {
        let colors: [Color] = [.white, .yellow, .cyan, .pink]
        return colors.randomElement() ?? .white
    }
    
    private func updateParticles() {
        let deltaTime = 1.0 / 60.0
        
        for i in particles.indices.reversed() {
            particles[i].update(deltaTime: deltaTime)
            
            // Yaşam süresi biten parçacıkları kaldır
            if particles[i].life <= 0 {
                particles.remove(at: i)
            }
        }
    }
}

// Tek parçacık görünümü
struct ParticleView: View {
    let particle: Particle
    
    var body: some View {
        Group {
            switch particle.shape {
            case .circle:
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    
            case .star:
                Image(systemName: "star.fill")
                    .font(.system(size: particle.size))
                    .foregroundColor(particle.color)
                    
            case .heart:
                Image(systemName: "heart.fill")
                    .font(.system(size: particle.size))
                    .foregroundColor(particle.color)
            }
        }
        .position(particle.position)
        .opacity(particle.opacity)
        .scaleEffect(particle.opacity) // Kaybolurken küçül
    }
}

// Konfeti efekti için özel view
struct ConfettiView: View {
    @State private var isActive = false
    let triggerPosition: CGPoint
    
    var body: some View {
        ParticleEffectView(
            effectType: .confetti,
            triggerPosition: triggerPosition,
            isActive: isActive
        )
        .onAppear {
            isActive = true
        }
    }
}

// Yıldız efekti için özel view
struct StarEffectView: View {
    @State private var isActive = false
    let triggerPosition: CGPoint
    
    var body: some View {
        ParticleEffectView(
            effectType: .stars,
            triggerPosition: triggerPosition,
            isActive: isActive
        )
        .onAppear {
            isActive = true
        }
    }
}

// Kalp efekti için özel view
struct HeartEffectView: View {
    @State private var isActive = false
    let triggerPosition: CGPoint
    
    var body: some View {
        ParticleEffectView(
            effectType: .hearts,
            triggerPosition: triggerPosition,
            isActive: isActive
        )
        .onAppear {
            isActive = true
        }
    }
}

// Işıltı efekti için özel view
struct SparkleEffectView: View {
    @State private var isActive = false
    let triggerPosition: CGPoint
    
    var body: some View {
        ParticleEffectView(
            effectType: .sparkles,
            triggerPosition: triggerPosition,
            isActive: isActive
        )
        .onAppear {
            isActive = true
        }
    }
}

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        ConfettiView(triggerPosition: CGPoint(x: 200, y: 400))
    }
}
