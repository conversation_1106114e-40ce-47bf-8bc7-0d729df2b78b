//
//  AnimationManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import CoreHaptics

// Gelişmiş animasyon yöneticisi
class AnimationManager: ObservableObject {
    @Published var isAnimating = false
    @Published var particleSystem = ParticleSystem()
    
    private var hapticEngine: CHHapticEngine?
    
    static let shared = AnimationManager()
    
    private init() {
        setupHaptics()
    }
    
    // MARK: - Haptic Feedback
    private func setupHaptics() {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else { return }
        
        do {
            hapticEngine = try CHHapticEngine()
            try hapticEngine?.start()
        } catch {
            print("Haptic engine başlatılamadı: \(error)")
        }
    }
    
    func playSuccessHaptic() {
        guard let hapticEngine = hapticEngine else { return }
        
        let intensity = CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0)
        let sharpness = CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
        
        let event = CHHapticEvent(eventType: .hapticTransient, parameters: [intensity, sharpness], relativeTime: 0)
        
        do {
            let pattern = try CHHapticPattern(events: [event], parameters: [])
            let player = try hapticEngine.makePlayer(with: pattern)
            try player.start(atTime: 0)
        } catch {
            print("Haptic feedback oynatılamadı: \(error)")
        }
    }
    
    func playErrorHaptic() {
        guard let hapticEngine = hapticEngine else { return }
        
        let intensity = CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.8)
        let sharpness = CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.3)
        
        let event1 = CHHapticEvent(eventType: .hapticTransient, parameters: [intensity, sharpness], relativeTime: 0)
        let event2 = CHHapticEvent(eventType: .hapticTransient, parameters: [intensity, sharpness], relativeTime: 0.1)
        
        do {
            let pattern = try CHHapticPattern(events: [event1, event2], parameters: [])
            let player = try hapticEngine.makePlayer(with: pattern)
            try player.start(atTime: 0)
        } catch {
            print("Haptic feedback oynatılamadı: \(error)")
        }
    }
    
    func playButtonTapHaptic() {
        guard let hapticEngine = hapticEngine else { return }
        
        let intensity = CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.6)
        let sharpness = CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.8)
        
        let event = CHHapticEvent(eventType: .hapticTransient, parameters: [intensity, sharpness], relativeTime: 0)
        
        do {
            let pattern = try CHHapticPattern(events: [event], parameters: [])
            let player = try hapticEngine.makePlayer(with: pattern)
            try player.start(atTime: 0)
        } catch {
            print("Haptic feedback oynatılamadı: \(error)")
        }
    }
}

// MARK: - Parçacık Sistemi
struct ParticleSystem {
    var particles: [Particle] = []
    
    mutating func addConfetti(at position: CGPoint, count: Int = 20) {
        let colors: [Color] = [.red, .orange, .yellow, .green, .blue, .purple, .pink]
        
        for _ in 0..<count {
            let particle = Particle(
                position: position,
                velocity: CGPoint(
                    x: Double.random(in: -200...200),
                    y: Double.random(in: -300...(-100))
                ),
                color: colors.randomElement() ?? .blue,
                size: Double.random(in: 4...12),
                life: 1.0,
                gravity: 500
            )
            particles.append(particle)
        }
    }
    
    mutating func addStars(at position: CGPoint, count: Int = 15) {
        for _ in 0..<count {
            let particle = Particle(
                position: position,
                velocity: CGPoint(
                    x: Double.random(in: -150...150),
                    y: Double.random(in: -200...200)
                ),
                color: .yellow,
                size: Double.random(in: 6...16),
                life: 1.5,
                gravity: 0,
                shape: .star
            )
            particles.append(particle)
        }
    }
    
    mutating func update(deltaTime: Double) {
        particles = particles.compactMap { particle in
            var updatedParticle = particle
            updatedParticle.update(deltaTime: deltaTime)
            return updatedParticle.life > 0 ? updatedParticle : nil
        }
    }
}

// MARK: - Parçacık Modeli
struct Particle {
    var position: CGPoint
    var velocity: CGPoint
    var color: Color
    var size: Double
    var life: Double
    var maxLife: Double
    var gravity: Double
    var shape: ParticleShape
    
    enum ParticleShape {
        case circle
        case star
        case heart
    }
    
    init(position: CGPoint, velocity: CGPoint, color: Color, size: Double, life: Double, gravity: Double = 300, shape: ParticleShape = .circle) {
        self.position = position
        self.velocity = velocity
        self.color = color
        self.size = size
        self.life = life
        self.maxLife = life
        self.gravity = gravity
        self.shape = shape
    }
    
    mutating func update(deltaTime: Double) {
        // Pozisyon güncelleme
        position.x += velocity.x * deltaTime
        position.y += velocity.y * deltaTime
        
        // Yerçekimi uygulama
        velocity.y += gravity * deltaTime
        
        // Yaşam süresi azaltma
        life -= deltaTime
        
        // Hız azaltma (sürtünme)
        velocity.x *= 0.98
        velocity.y *= 0.98
    }
    
    var opacity: Double {
        return life / maxLife
    }
}

// MARK: - Animasyon Sabitleri
struct AnimationConstants {
    static let springResponse: Double = 0.6
    static let springDamping: Double = 0.8
    static let buttonPressScale: Double = 0.95
    static let cardHoverScale: Double = 1.05
    static let fadeInDuration: Double = 0.3
    static let slideInDuration: Double = 0.5
    static let bounceInDuration: Double = 0.8
    static let pulseInDuration: Double = 1.2
}

// MARK: - Özel Animasyon Modifierleri
struct BounceInModifier: ViewModifier {
    @State private var scale: CGFloat = 0.3
    @State private var opacity: Double = 0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .opacity(opacity)
            .onAppear {
                withAnimation(.spring(response: AnimationConstants.bounceInDuration, dampingFraction: 0.6)) {
                    scale = 1.0
                    opacity = 1.0
                }
            }
    }
}

struct SlideInModifier: ViewModifier {
    let direction: SlideDirection
    @State private var offset: CGFloat = 100
    @State private var opacity: Double = 0
    
    enum SlideDirection {
        case left, right, top, bottom
    }
    
    func body(content: Content) -> some View {
        content
            .offset(
                x: direction == .left ? -offset : (direction == .right ? offset : 0),
                y: direction == .top ? -offset : (direction == .bottom ? offset : 0)
            )
            .opacity(opacity)
            .onAppear {
                withAnimation(.easeOut(duration: AnimationConstants.slideInDuration)) {
                    offset = 0
                    opacity = 1.0
                }
            }
    }
}

struct PulseModifier: ViewModifier {
    @State private var scale: CGFloat = 1.0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .onAppear {
                withAnimation(.easeInOut(duration: AnimationConstants.pulseInDuration).repeatForever(autoreverses: true)) {
                    scale = 1.1
                }
            }
    }
}

// MARK: - View Extensions
extension View {
    func bounceIn() -> some View {
        modifier(BounceInModifier())
    }
    
    func slideIn(from direction: SlideInModifier.SlideDirection) -> some View {
        modifier(SlideInModifier(direction: direction))
    }
    
    func pulse() -> some View {
        modifier(PulseModifier())
    }
    
    func buttonPress() -> some View {
        scaleEffect(1.0)
        .animation(.spring(response: AnimationConstants.springResponse, dampingFraction: AnimationConstants.springDamping), value: UUID())
    }
}
