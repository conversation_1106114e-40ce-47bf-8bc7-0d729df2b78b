//
//  ThemeManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import Foundation

// Tema Yöneticisi
class ThemeManager: ObservableObject {
    @Published var currentTheme: AppTheme = .dynamic
    
    enum AppTheme: String, CaseIterable {
        case dynamic = "dynamic"
        case classic = "classic"
        case neon = "neon"
        case sunset = "sunset"

        var displayName: String {
            switch self {
            case .dynamic: return NSLocalizedString("theme.dynamic", comment: "")
            case .classic: return NSLocalizedString("theme.classic", comment: "")
            case .neon: return NSLocalizedString("theme.neon", comment: "")
            case .sunset: return NSLocalizedString("theme.sunset", comment: "")
            }
        }
    }
    
    // Singleton instance
    static let shared = ThemeManager()
    
    private init() {
        // Kaydedilmiş temayı yükle
        if let savedTheme = UserDefaults.standard.string(forKey: "selectedTheme"),
           let theme = AppTheme(rawValue: savedTheme) {
            currentTheme = theme
        }
    }
    
    func setTheme(_ theme: AppTheme) {
        currentTheme = theme
        UserDefaults.standard.set(theme.rawValue, forKey: "selectedTheme")
    }
    
    // Dinamik renk hesaplama (günün saatine göre)
    var backgroundGradient: LinearGradient {
        let hour = Calendar.current.component(.hour, from: Date())
        
        switch currentTheme {
        case .dynamic:
            return getDynamicGradient(for: hour)
        case .classic:
            return getClassicGradient()
        case .neon:
            return getNeonGradient()
        case .sunset:
            return getSunsetGradient()
        }
    }
    
    private func getDynamicGradient(for hour: Int) -> LinearGradient {
        switch hour {
        case 6..<12: // Sabah
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 1.0, green: 0.8, blue: 0.4), // Altın sarısı
                    Color(red: 0.9, green: 0.5, blue: 0.2)  // Turuncu
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case 12..<18: // Öğlen
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.2, green: 0.6, blue: 1.0), // Gökyüzü mavisi
                    Color(red: 0.4, green: 0.8, blue: 0.9)  // Açık mavi
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case 18..<22: // Akşam
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.8, green: 0.3, blue: 0.5), // Pembe
                    Color(red: 0.9, green: 0.4, blue: 0.2)  // Turuncu
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        default: // Gece
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3), // Koyu mavi
                    Color(red: 0.2, green: 0.1, blue: 0.4)  // Mor
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    private func getClassicGradient() -> LinearGradient {
        return LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.8, green: 0.1, blue: 0.1), // Klasik kırmızı
                Color(red: 1.0, green: 0.8, blue: 0.0)  // Altın
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private func getNeonGradient() -> LinearGradient {
        return LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.0, green: 1.0, blue: 0.8), // Neon turkuaz
                Color(red: 0.5, green: 0.0, blue: 1.0)  // Neon mor
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private func getSunsetGradient() -> LinearGradient {
        return LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 1.0, green: 0.4, blue: 0.4), // Pembe
                Color(red: 1.0, green: 0.6, blue: 0.0), // Turuncu
                Color(red: 1.0, green: 0.8, blue: 0.2)  // Sarı
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // Buton renkleri
    var primaryButtonGradient: LinearGradient {
        switch currentTheme {
        case .dynamic:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 1.0, green: 0.3, blue: 0.3),
                    Color(red: 1.0, green: 0.5, blue: 0.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .classic:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.8, green: 0.1, blue: 0.1),
                    Color(red: 1.0, green: 0.8, blue: 0.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .neon:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.0, green: 1.0, blue: 0.5),
                    Color(red: 0.0, green: 0.8, blue: 1.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .sunset:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 1.0, green: 0.4, blue: 0.4),
                    Color(red: 1.0, green: 0.6, blue: 0.0)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }
    
    var secondaryButtonGradient: LinearGradient {
        switch currentTheme {
        case .dynamic:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.3, green: 0.5, blue: 1.0),
                    Color(red: 0.5, green: 0.3, blue: 0.8)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .classic:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.2, green: 0.3, blue: 0.6),
                    Color(red: 0.4, green: 0.2, blue: 0.5)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .neon:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.8, green: 0.0, blue: 1.0),
                    Color(red: 1.0, green: 0.0, blue: 0.5)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case .sunset:
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.6, green: 0.3, blue: 0.8),
                    Color(red: 0.8, green: 0.2, blue: 0.6)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }
    
    // Kategori renkleri
    func getCategoryGradient(for categoryKey: String) -> LinearGradient {
        switch categoryKey {
        case "world_cuisine":
            return LinearGradient(
                gradient: Gradient(colors: [Color.orange, Color.red]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "turkish_cuisine":
            return LinearGradient(
                gradient: Gradient(colors: [Color.red, Color(red: 0.8, green: 0.1, blue: 0.1)]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "tech_brands":
            return LinearGradient(
                gradient: Gradient(colors: [Color.blue, Color.cyan]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "netflix_shows":
            return LinearGradient(
                gradient: Gradient(colors: [Color.red, Color(red: 0.5, green: 0.0, blue: 0.0)]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "turkish_celebrities":
            return LinearGradient(
                gradient: Gradient(colors: [Color.teal, Color.blue]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "world_cities":
            return LinearGradient(
                gradient: Gradient(colors: [Color.green, Color.mint]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "superheroes":
            return LinearGradient(
                gradient: Gradient(colors: [Color.yellow, Color.orange]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "footballers":
            return LinearGradient(
                gradient: Gradient(colors: [Color.purple, Color.indigo]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "historical_figures":
            return LinearGradient(
                gradient: Gradient(colors: [Color.brown, Color(red: 0.6, green: 0.4, blue: 0.2)]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "world_musicians":
            return LinearGradient(
                gradient: Gradient(colors: [Color.pink, Color.purple]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "world_actors":
            return LinearGradient(
                gradient: Gradient(colors: [Color.indigo, Color.blue]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "bollywood_celebrities":
            return LinearGradient(
                gradient: Gradient(colors: [Color.cyan, Color.teal]),
                startPoint: .leading,
                endPoint: .trailing
            )
        case "animals":
            return LinearGradient(
                gradient: Gradient(colors: [Color.mint, Color.green]),
                startPoint: .leading,
                endPoint: .trailing
            )
        default:
            return LinearGradient(
                gradient: Gradient(colors: [Color.gray, Color.secondary]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }

    // Kategori arka plan görselleri (SF Symbols kullanarak)
    func getCategoryBackgroundImage(for categoryKey: String) -> String {
        switch categoryKey {
        case "world_cuisine":
            return "fork.knife.circle.fill"
        case "turkish_cuisine":
            return "moon.stars.fill"
        case "tech_brands":
            return "cpu.fill"
        case "netflix_shows":
            return "tv.circle.fill"
        case "turkish_celebrities":
            return "star.circle.fill"
        case "world_cities":
            return "building.2.crop.circle.fill"
        case "superheroes":
            return "bolt.circle.fill"
        case "footballers":
            return "sportscourt.circle.fill"
        case "historical_figures":
            return "book.circle.fill"
        case "world_musicians":
            return "music.note.list"
        case "world_actors":
            return "film.circle.fill"
        case "bollywood_celebrities":
            return "sparkles"
        case "animals":
            return "pawprint.circle.fill"
        default:
            return "questionmark.circle.fill"
        }
    }

    // Kategori için dekoratif elementler
    func getCategoryDecorations(for categoryKey: String) -> [String] {
        switch categoryKey {
        case "world_cuisine":
            return ["fork.knife", "cup.and.saucer.fill", "wineglass.fill"]
        case "turkish_cuisine":
            return ["moon.fill", "star.fill", "leaf.fill"]
        case "tech_brands":
            return ["laptopcomputer", "iphone", "applewatch"]
        case "netflix_shows":
            return ["tv.fill", "play.circle.fill", "popcorn.fill"]
        case "turkish_celebrities":
            return ["star.fill", "heart.fill", "crown.fill"]
        case "world_cities":
            return ["building.2.fill", "car.fill", "airplane"]
        case "superheroes":
            return ["bolt.fill", "shield.fill", "flame.fill"]
        case "footballers":
            return ["soccerball", "trophy.fill", "flag.fill"]
        case "historical_figures":
            return ["book.fill", "scroll.fill", "crown.fill"]
        case "world_musicians":
            return ["music.note", "guitars.fill", "mic.fill"]
        case "world_actors":
            return ["film.fill", "camera.fill", "theatermasks.fill"]
        case "bollywood_celebrities":
            return ["sparkles", "star.fill", "heart.fill"]
        case "animals":
            return ["pawprint.fill", "leaf.fill", "heart.fill"]
        default:
            return ["questionmark.circle.fill"]
        }
    }
    
    // Animasyonlu arka plan parçacıkları için renkler
    var particleColors: [Color] {
        switch currentTheme {
        case .dynamic:
            return [Color.white.opacity(0.1), Color.white.opacity(0.05)]
        case .classic:
            return [Color.white.opacity(0.2), Color.yellow.opacity(0.1)]
        case .neon:
            return [Color.cyan.opacity(0.3), Color.pink.opacity(0.2)]
        case .sunset:
            return [Color.orange.opacity(0.2), Color.pink.opacity(0.1)]
        }
    }
}
