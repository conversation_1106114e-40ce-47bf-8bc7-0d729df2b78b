//
//  OnboardingView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

struct OnboardingView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var onboardingManager = OnboardingManager.shared
    @StateObject private var audioManager = AudioManager.shared
    @State private var currentStep = 0

    let totalSteps = 4

    // Completion callback
    let onCompletion: (() -> Void)?

    init(onCompletion: (() -> Void)? = nil) {
        self.onCompletion = onCompletion
    }
    
    var body: some View {
        ZStack {
            // Dinamik arka plan gradyanı
            themeManager.backgroundGradient
                .ignoresSafeArea()
                .animation(.easeInOut(duration: 2.0), value: themeManager.currentTheme)
            
            // Animasyonlu arka plan desenleri
            ForEach(0..<6) { index in
                Circle()
                    .fill(themeManager.particleColors.randomElement() ?? Color.white.opacity(0.1))
                    .frame(width: CGFloat.random(in: 20...80))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .animation(
                        Animation.easeInOut(duration: Double.random(in: 4...8))
                            .repeatForever(autoreverses: true)
                            .delay(Double.random(in: 0...2)),
                        value: UUID()
                    )
            }
            
            VStack(spacing: 0) {
                // Üst kısım - Progress ve Skip butonu
                HStack {
                    // Progress indicator
                    HStack(spacing: 8) {
                        ForEach(0..<totalSteps, id: \.self) { index in
                            Circle()
                                .fill(index <= currentStep ? Color.white : Color.white.opacity(0.3))
                                .frame(width: 12, height: 12)
                                .scaleEffect(index == currentStep ? 1.2 : 1.0)
                                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentStep)
                        }
                    }
                    
                    Spacer()
                    
                    // Skip butonu
                    Button(action: {
                        AnimationManager.shared.playButtonTapHaptic()
                        onboardingManager.skipOnboarding()
                        dismiss()
                        onCompletion?()
                    }) {
                        Text(NSLocalizedString("skip", comment: ""))
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.white.opacity(0.2))
                            )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
                
                // Ana içerik
                TabView(selection: $currentStep) {
                    // Step 0: Hoş geldin
                    OnboardingStepView(
                        step: 0,
                        icon: "party.popper.fill",
                        title: NSLocalizedString("onboarding_welcome_title", comment: ""),
                        description: NSLocalizedString("onboarding_welcome_description", comment: ""),
                        animation: .scale.combined(with: .opacity)
                    )
                    .tag(0)
                    
                    // Step 1: Nasıl oynanır
                    OnboardingStepView(
                        step: 1,
                        icon: "gamecontroller.fill",
                        title: NSLocalizedString("onboarding_how_to_play_title", comment: ""),
                        description: NSLocalizedString("onboarding_how_to_play_description", comment: ""),
                        animation: .move(edge: .leading).combined(with: .opacity)
                    )
                    .tag(1)
                    
                    // Step 2: Telefon hareketleri
                    OnboardingStepView(
                        step: 2,
                        icon: "iphone",
                        title: NSLocalizedString("onboarding_phone_movements_title", comment: ""),
                        description: NSLocalizedString("onboarding_phone_movements_description", comment: ""),
                        animation: .move(edge: .trailing).combined(with: .opacity),
                        showPhoneDemo: true
                    )
                    .tag(2)
                    
                    // Step 3: Kategoriler
                    OnboardingStepView(
                        step: 3,
                        icon: "square.grid.3x3.fill",
                        title: NSLocalizedString("onboarding_categories_title", comment: ""),
                        description: NSLocalizedString("onboarding_categories_description", comment: ""),
                        animation: .move(edge: .bottom).combined(with: .opacity)
                    )
                    .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.5), value: currentStep)
                
                // Alt kısım - Butonlar
                VStack(spacing: 20) {
                    if currentStep < totalSteps - 1 {
                        // İleri butonu
                        AnimatedButton(
                            title: NSLocalizedString("next", comment: ""),
                            action: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentStep += 1
                                }
                                AnalyticsManager.shared.logOnboardingStepViewed(step: currentStep + 1)
                            },
                            style: .glow,
                            gradient: themeManager.primaryButtonGradient
                        )
                        .slideIn(from: .bottom)
                    } else {
                        // Başla butonu
                        AnimatedButton(
                            title: NSLocalizedString("start_playing", comment: ""),
                            action: {
                                onboardingManager.completeOnboarding()
                                dismiss()
                                onCompletion?()
                            },
                            style: .glow,
                            gradient: themeManager.primaryButtonGradient
                        )
                        .slideIn(from: .bottom)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            AnalyticsManager.shared.logOnboardingStart()
            AnalyticsManager.shared.logOnboardingStepViewed(step: 0)
            audioManager.playBackgroundMusic()
        }
        .onDisappear {
            audioManager.cleanup()
        }
        .onChange(of: currentStep) { newStep in
            AnimationManager.shared.playButtonTapHaptic()
        }
    }
}
