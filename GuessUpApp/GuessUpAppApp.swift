//
//  GuessUpAppApp.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import ObjectiveC
import GoogleMobileAds
import StoreKit

@main
struct GuessUpAppApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate
    @AppStorage("language") private var language = getInitialLanguage()
    
    init() {
        // AdMob'u başlat
        MobileAds.initialize()
        
        // Test cihazı tanımlayıcısını ayarla
        MobileAds.shared.requestConfiguration.testDeviceIdentifiers = ["ac7f67265a05b4d1e3c3fd2f17f9d25e"]
        
        // Ekran kararmasını devre dışı bırak
        UIApplication.shared.isIdleTimerDisabled = true
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.locale, .init(identifier: language))
                .onChange(of: language) { newValue in
                    // Dil değişikliğini UserDefaults'a kaydet
                    UserDefaults.standard.set([newValue], forKey: "AppleLanguages")
                    UserDefaults.standard.synchronize()
                    
                    // Bundle'ı güncelle
                    if let languageBundle = Bundle(path: Bundle.main.path(forResource: newValue, ofType: "lproj") ?? "") {
                        objc_setAssociatedObject(Bundle.main, &bundleKey, languageBundle, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
                    }
                    
                    // Bundle'ı yeniden yükle
                    Bundle.main.localizations.forEach { locale in
                        if locale == newValue {
                            if let bundlePath = Bundle.main.path(forResource: locale, ofType: "lproj"),
                               let bundle = Bundle(path: bundlePath) {
                                objc_setAssociatedObject(Bundle.main, &bundleKey, bundle, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
                            }
                        }
                    }
                    
                    // Bildirim gönder
                    NotificationCenter.default.post(name: NSNotification.Name("LanguageChanged"), object: nil)
                    
                    // Uygulamayı yeniden başlat
                    exit(0)
                }
        }
    }
}

private var bundleKey: UInt8 = 0

extension Bundle {
    static func localizedString(for key: String, value: String? = nil) -> String {
        let bundle = objc_getAssociatedObject(Bundle.main, &bundleKey) as? Bundle ?? Bundle.main
        return bundle.localizedString(forKey: key, value: value, table: nil)
    }
}

private func getInitialLanguage() -> String {
    // Desteklenen diller
    let supportedLanguages = ["tr", "en", "de", "fr", "es", "it", "ru", "ja", "ko", "zh", "ar"]
    
    // Sistem dilini al
    let preferredLanguage = Bundle.main.preferredLocalizations.first ?? "en"
    
    // Eğer sistem dili desteklenen diller arasındaysa onu kullan, değilse İngilizce'ye dön
    return supportedLanguages.contains(preferredLanguage) ? preferredLanguage : "en"
}
