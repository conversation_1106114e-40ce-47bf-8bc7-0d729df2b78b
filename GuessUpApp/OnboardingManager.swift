//
//  OnboardingManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import Foundation
import SwiftUI
import FirebaseAnalytics

class OnboardingManager: ObservableObject {
    static let shared = OnboardingManager()
    
    private init() {}
    
    // MARK: - UserDefaults Keys
    private let hasCompletedOnboardingKey = "has_completed_onboarding"
    private let onboardingVersionKey = "onboarding_version"
    
    // MARK: - Properties
    @AppStorage("has_completed_onboarding") private var hasCompletedOnboarding = false
    @AppStorage("onboarding_version") private var onboardingVersion = 0
    
    // Current onboarding version - increment this when you want to show onboarding again
    private let currentOnboardingVersion = 1
    
    // MARK: - Public Properties
    var shouldShowOnboarding: Bool {
        return !hasCompletedOnboarding || onboardingVersion < currentOnboardingVersion
    }
    
    // MARK: - Public Methods
    func completeOnboarding() {
        hasCompletedOnboarding = true
        onboardingVersion = currentOnboardingVersion
        AnalyticsManager.shared.logOnboardingComplete()
    }
    
    func resetOnboarding() {
        hasCompletedOnboarding = false
        onboardingVersion = 0
    }
    
    func skipOnboarding() {
        completeOnboarding()
        AnalyticsManager.shared.logOnboardingSkipped()
    }
}

// MARK: - Analytics Extension
extension AnalyticsManager {
    func logOnboardingStart() {
        Analytics.logEvent("onboarding_start", parameters: [:])
    }

    func logOnboardingComplete() {
        Analytics.logEvent("onboarding_complete", parameters: [:])
    }

    func logOnboardingSkipped() {
        Analytics.logEvent("onboarding_skipped", parameters: [:])
    }

    func logOnboardingStepViewed(step: Int) {
        Analytics.logEvent("onboarding_step_viewed", parameters: [
            "step_number": step
        ])
    }
}
