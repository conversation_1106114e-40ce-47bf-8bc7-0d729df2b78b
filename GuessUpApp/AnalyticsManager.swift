import Foundation
import FirebaseAnalytics

class AnalyticsManager {
    static let shared = AnalyticsManager()
    
    private init() {}
    
    // MARK: - <PERSON><PERSON> Başlangıç ve Bitiş
    func logGameStart(category: String) {
        Analytics.logEvent("game_start", parameters: [
            "category": category
        ])
    }
    
    func logGameEnd(category: String, duration: Int, team1Score: Int, team2Score: Int, team1Words: Int, team2Words: Int) {
        Analytics.logEvent("game_end", parameters: [
            "category": category,
            "duration": duration,
            "correct_guesses": team1Score,
            "incorrect_guesses": team2Score
        ])
    }
    
    // MARK: - <PERSON><PERSON><PERSON>
    func logCategorySelect(category: String) {
        Analytics.logEvent("category_select", parameters: [
            "category": category
        ])
    }

    func logCategoryUnlock(category: String, method: String) {
        Analytics.logEvent("category_unlock", parameters: [
            "category": category,
            "unlock_method": method // "ad_reward" veya "purchase"
        ])
    }

    func logAdRequest(category: String) {
        Analytics.logEvent("ad_request_for_category", parameters: [
            "category": category
        ])
    }
    
    // MARK: - Ayarlar
    func logGameDurationChange(newDuration: Int) {
        Analytics.logEvent("game_duration_change", parameters: [
            "new_duration": newDuration
        ])
    }
    
    func logLanguageChange(newLanguage: String) {
        Analytics.logEvent("language_change", parameters: [
            "new_language": newLanguage
        ])
    }

    func logMotionSensitivityChange(newSensitivity: Int) {
        Analytics.logEvent("motion_sensitivity_change", parameters: [
            "new_sensitivity": newSensitivity
        ])
    }
    
    // MARK: - Oyun İçi Olaylar
    func logCorrectGuess(category: String, word: String) {
        Analytics.logEvent("correct_guess", parameters: [
            "category": category,
            "word": word
        ])
    }
    
    func logIncorrectGuess(category: String, word: String) {
        Analytics.logEvent("incorrect_guess", parameters: [
            "category": category,
            "word": word
        ])
    }
    
    func logRoundComplete(round: Int, team: Int, score: Int) {
        Analytics.logEvent("round_complete", parameters: [
            "round": round,
            "score": score
        ])
    }
    
    // MARK: - Uygulama Kullanımı
    func logAppOpen() {
        Analytics.logEvent("app_open", parameters: nil)
    }
    
    func logSettingsOpen() {
        Analytics.logEvent("settings_open", parameters: nil)
    }
    
    func logGameExit() {
        Analytics.logEvent("game_exit", parameters: nil)
    }

    // MARK: - Takım Oyunu Olayları
    func logTeamGameStart(category: String, team1Name: String, team2Name: String, rounds: Int) {
        Analytics.logEvent("team_game_start", parameters: [
            "category": category,
            "team1_name": team1Name,
            "team2_name": team2Name,
            "rounds": rounds
        ])
    }

    func logRoundEnd(category: String, round: Int, team: Int, teamName: String, correctGuesses: Int, incorrectGuesses: Int) {
        Analytics.logEvent("round_end", parameters: [
            "category": category,
            "round": round,
            "team": team,
            "team_name": teamName,
            "correct_guesses": correctGuesses,
            "incorrect_guesses": incorrectGuesses
        ])
    }

    func logGameComplete(category: String, team1Name: String, team2Name: String, team1Score: Int, team2Score: Int, rounds: Int) {
        Analytics.logEvent("team_game_complete", parameters: [
            "category": category,
            "team1_name": team1Name,
            "team2_name": team2Name,
            "team1_score": team1Score,
            "team2_score": team2Score,
            "rounds": rounds,
            "winner": team1Score > team2Score ? "team1" : (team2Score > team1Score ? "team2" : "tie")
        ])
    }

    // MARK: - App Store Değerlendirme
    func logRatingRequest() {
        Analytics.logEvent("rating_request_shown", parameters: nil)
    }

    func logRatingCompleted() {
        Analytics.logEvent("rating_completed", parameters: nil)
    }

    func logRatingDismissed() {
        Analytics.logEvent("rating_dismissed", parameters: nil)
    }

    // MARK: - Subscription Events
    func logSubscriptionOfferShown() {
        Analytics.logEvent("subscription_offer_shown", parameters: nil)
    }

    func logSubscriptionPurchase(productId: String) {
        Analytics.logEvent("subscription_purchase", parameters: [
            "product_id": productId
        ])
    }

    func logSubscriptionCancelled() {
        Analytics.logEvent("subscription_cancelled", parameters: nil)
    }

    func logSubscriptionRestored() {
        Analytics.logEvent("subscription_restored", parameters: nil)
    }

    func logSubscriptionOfferDismissed() {
        Analytics.logEvent("subscription_offer_dismissed", parameters: nil)
    }

    func logSubscriptionManageOpened() {
        Analytics.logEvent("subscription_manage_opened", parameters: nil)
    }
}