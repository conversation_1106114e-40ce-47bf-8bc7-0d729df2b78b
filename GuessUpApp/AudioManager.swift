//
//  AudioManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import Foundation
import AVFoundation
import AudioToolbox

class AudioManager: ObservableObject {
    static let shared = AudioManager()
    
    @Published var isMusicEnabled = true
    @Published var areSoundEffectsEnabled = true
    
    private var backgroundMusicPlayer: AVAudioPlayer?
    private var applausePlayer: AVAudioPlayer?
    private var partyMusicPlayer: AVAudioPlayer?
    
    private init() {
        setupAudioSession()
        loadUserPreferences()
    }
    
    // MARK: - Audio Session Setup
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Audio session setup hatası: \(error)")
        }
    }
    
    // MARK: - User Preferences
    private func loadUserPreferences() {
        isMusicEnabled = UserDefaults.standard.bool(forKey: "party_music_enabled")
        areSoundEffectsEnabled = UserDefaults.standard.bool(forKey: "sound_effects_enabled")
    }
    
    func toggleMusic() {
        isMusicEnabled.toggle()
        UserDefaults.standard.set(isMusicEnabled, forKey: "party_music_enabled")
        
        if isMusicEnabled {
            playBackgroundMusic()
        } else {
            stopBackgroundMusic()
        }
    }
    
    func toggleSoundEffects() {
        areSoundEffectsEnabled.toggle()
        UserDefaults.standard.set(areSoundEffectsEnabled, forKey: "sound_effects_enabled")
    }
    
    // MARK: - Background Music
    func playBackgroundMusic() {
        guard isMusicEnabled else { return }
        
        // Sistem ses dosyalarını kullanarak basit bir parti müziği efekti
        // Gerçek müzik dosyası eklemek için bundle'a ses dosyası eklenmeli
        stopBackgroundMusic()
        
        // Şimdilik sistem seslerini kullanıyoruz
        DispatchQueue.global(qos: .background).async {
            self.playPartyAmbience()
        }
    }
    
    func stopBackgroundMusic() {
        backgroundMusicPlayer?.stop()
        backgroundMusicPlayer = nil
    }
    
    private func playPartyAmbience() {
        // Parti atmosferi için periyodik olarak hafif ses efektleri
        guard isMusicEnabled else { return }
        
        Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { timer in
            if !self.isMusicEnabled {
                timer.invalidate()
                return
            }
            
            // Hafif parti sesi efekti
            if self.areSoundEffectsEnabled {
                AudioServicesPlaySystemSound(1016) // Hafif ding sesi
            }
        }
    }
    
    // MARK: - Sound Effects
    func playCorrectSound() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1104) // Success sound
    }
    
    func playIncorrectSound() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1107) // Error sound
    }
    
    func playApplause() {
        guard areSoundEffectsEnabled else { return }
        
        // Alkış efekti için birden fazla ses
        DispatchQueue.main.async {
            AudioServicesPlaySystemSound(1016)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            AudioServicesPlaySystemSound(1016)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            AudioServicesPlaySystemSound(1016)
        }
    }
    
    func playGameStartSound() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1013) // Tweet sound
    }
    
    func playGameEndSound() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1025) // Fanfare-like sound
    }
    
    func playRoundChangeSound() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1003) // Tri-tone
    }
    
    func playCountdownSound() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1004) // Vibrant
    }
    
    func playPartyWhistle() {
        guard areSoundEffectsEnabled else { return }
        AudioServicesPlaySystemSound(1020) // Whistle-like sound
    }
    
    // MARK: - Cleanup
    func cleanup() {
        stopBackgroundMusic()
        applausePlayer?.stop()
        partyMusicPlayer?.stop()
    }
}
