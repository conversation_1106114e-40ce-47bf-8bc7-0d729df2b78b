import UIKit
import FirebaseCore
import FirebaseRemoteConfig
import GoogleMobileAds

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Firebase'i initialize et
        FirebaseApp.configure()

        // Remote Config'i başlat
        setupRemoteConfig()

        return true
    }

    private func setupRemoteConfig() {
        let remoteConfig = RemoteConfig.remoteConfig()
        let settings = RemoteConfigSettings()
        settings.minimumFetchInterval = 0 // Development için, production'da 3600 (1 saat) olmalı
        remoteConfig.configSettings = settings

        // Default değerler
        remoteConfig.setDefaults([
            "paywall_variant": "original" as NSObject,
            "show_social_proof": true as NSObject,
            "trial_duration_days": 7 as NSObject,
            "discount_percentage": 50 as NSObject,
            "paywall_title": "Unlock Premium Features" as NSObject,
            "paywall_subtitle": "Get unlimited access to all categories" as NSObject
        ])

        // Remote config'i fetch et
        remoteConfig.fetch { status, error in
            if status == .success {
                remoteConfig.activate { _, _ in
                    print("Remote Config activated successfully")
                }
            } else {
                print("Remote Config fetch failed: \(error?.localizedDescription ?? "Unknown error")")
            }
        }
    }

    // iPad için sadece yatay yönlendirmeyi destekle
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        if UIDevice.current.userInterfaceIdiom == .pad {
            return .landscape
        } else {
            return .allButUpsideDown
        }
    }
}
