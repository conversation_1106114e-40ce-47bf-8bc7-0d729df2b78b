//
//  OnboardingStepView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

struct OnboardingStepView: View {
    let step: Int
    let icon: String
    let title: String
    let description: String
    let animation: AnyTransition
    let showPhoneDemo: Bool
    
    @StateObject private var themeManager = ThemeManager.shared
    @State private var phoneRotation: Double = 0
    @State private var showCorrectIcon = false
    @State private var showIncorrectIcon = false
    @State private var demoTimer: Timer?
    
    init(step: Int, icon: String, title: String, description: String, animation: AnyTransition, showPhoneDemo: Bool = false) {
        self.step = step
        self.icon = icon
        self.title = title
        self.description = description
        self.animation = animation
        self.showPhoneDemo = showPhoneDemo
    }
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Ana ikon veya telefon demo
            if showPhoneDemo {
                phoneMovementDemo
            } else {
                mainIcon
            }
            
            // Başlık ve açıklama
            VStack(spacing: 20) {
                Text(title)
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            gradient: Gradient(colors: [.white, .yellow]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .multilineTextAlignment(.center)
                    .shadow(color: .black.opacity(0.3), radius: 5, x: 0, y: 2)
                
                Text(description)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, 20)
            }
            
            Spacer()
        }
        .padding(.horizontal, 30)
        .transition(animation)
        .onAppear {
            if showPhoneDemo {
                startPhoneDemo()
            }
        }
        .onDisappear {
            demoTimer?.invalidate()
            demoTimer = nil
        }
    }
    
    private var mainIcon: some View {
        Image(systemName: icon)
            .font(.system(size: 120))
            .foregroundStyle(
                LinearGradient(
                    gradient: Gradient(colors: themeManager.particleColors),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .padding(40)
            .background(
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
            )
            .pulse()
    }
    
    private var phoneMovementDemo: some View {
        ZStack {
            // Telefon (yatay pozisyon)
            RoundedRectangle(cornerRadius: 25)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [.gray.opacity(0.8), .black.opacity(0.6)]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 200, height: 120) // Yatay boyutlar
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(Color.white.opacity(0.3), lineWidth: 2)
                )
                .overlay(
                    // Ekran
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.blue.opacity(0.8))
                        .frame(width: 160, height: 100) // Yatay ekran
                        .overlay(
                            Text("GuessUp!")
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(.white)
                        )
                )
                .overlay(
                    // Home indicator (alt kısım)
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.6))
                        .frame(width: 30, height: 4)
                        .offset(y: 45)
                )
                .rotation3DEffect(
                    .degrees(phoneRotation),
                    axis: (x: 1.0, y: 0.0, z: 0.0), // X ekseni etrafında döndürme (öne/geriye eğme)
                    perspective: 0.5
                )
                .animation(.easeInOut(duration: 1.5), value: phoneRotation)
            

            
            // Sade hareket talimatları
            VStack(spacing: 16) {
                // Üst kısım - Doğru hareket
                HStack(spacing: 8) {
                    Image(systemName: "arrow.up")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.green)

                    Image(systemName: "iphone")
                        .font(.system(size: 18))
                        .foregroundColor(.white)
                        .rotationEffect(.degrees(-8))

                    Image(systemName: "checkmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.green)

                    Text(NSLocalizedString("tilt_forward_correct", comment: ""))
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(showCorrectIcon ? 1.0 : 0.9))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(showCorrectIcon ? 0.4 : 0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.green.opacity(showCorrectIcon ? 0.6 : 0.1), lineWidth: showCorrectIcon ? 2 : 1)
                        )
                )
                .scaleEffect(showCorrectIcon ? 1.08 : 1.0)
                .animation(.easeInOut(duration: 0.3), value: showCorrectIcon)

                Spacer()
                    .frame(height: 140) // Telefon için boşluk

                // Alt kısım - Yanlış hareket
                HStack(spacing: 8) {
                    Image(systemName: "arrow.down")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.red)

                    Image(systemName: "iphone")
                        .font(.system(size: 18))
                        .foregroundColor(.white)
                        .rotationEffect(.degrees(8))

                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.red)

                    Text(NSLocalizedString("tilt_backward_incorrect", comment: ""))
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(showIncorrectIcon ? 1.0 : 0.9))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(showIncorrectIcon ? 0.4 : 0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red.opacity(showIncorrectIcon ? 0.6 : 0.1), lineWidth: showIncorrectIcon ? 2 : 1)
                        )
                )
                .scaleEffect(showIncorrectIcon ? 1.08 : 1.0)
                .animation(.easeInOut(duration: 0.3), value: showIncorrectIcon)
            }
            .frame(width: 280, height: 280)
        }
    }
    
    private func startPhoneDemo() {
        demoTimer = Timer.scheduledTimer(withTimeInterval: 4.0, repeats: true) { _ in
            // Doğru hareket (öne eğme - negatif rotasyon)
            withAnimation(.easeInOut(duration: 1.0)) {
                phoneRotation = -25 // Öne eğme
                showCorrectIcon = true
                showIncorrectIcon = false
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                // Nötr pozisyon
                withAnimation(.easeInOut(duration: 0.5)) {
                    phoneRotation = 0
                    showCorrectIcon = false
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    // Yanlış hareket (geriye eğme - pozitif rotasyon)
                    withAnimation(.easeInOut(duration: 1.0)) {
                        phoneRotation = 25 // Geriye eğme
                        showIncorrectIcon = true
                    }

                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        // Nötr pozisyona dön
                        withAnimation(.easeInOut(duration: 0.5)) {
                            phoneRotation = 0
                            showIncorrectIcon = false
                        }
                    }
                }
            }
        }

        // İlk demo'yu hemen başlat
        demoTimer?.fire()
    }
}
