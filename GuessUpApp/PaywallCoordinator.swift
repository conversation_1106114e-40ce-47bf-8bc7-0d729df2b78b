//
//  PaywallCoordinator.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI

struct PaywallCoordinator: View {
    @StateObject private var variantManager = PaywallVariantManager.shared
    @Environment(\.dismiss) private var dismiss
    @State private var showStartTime = Date()

    var body: some View {
        Group {
            switch variantManager.currentVariant {
            case .original:
                SubscriptionOfferView()
            case .trialToggle:
                TrialTogglePaywallView()
            case .socialProof:
                SocialProofPaywallView()
            case .valueFirst:
                ValueFirstPaywallView()
            case .honest:
                HonestPaywallView()
            case .personalized:
                PersonalizedPaywallView()
            }
        }
        .onAppear {
            showStartTime = Date()

            // Analytics - Paywall gösterildi
            AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())

            // Remote config'i refresh et
            Task {
                await variantManager.refreshRemoteConfig()
            }
        }
        .onDisappear {
            // Analytics - Paywall kapatıldı
            let timeSpent = Date().timeIntervalSince(showStartTime)
            AnalyticsManager.shared.logPaywallDismissed(
                variant: variantManager.getCurrentVariantForAnalytics(),
                timeSpent: timeSpent
            )
        }
    }
}

struct PersonalizedPaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var variantManager = PaywallVariantManager.shared
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var animateContent = false
    @State private var showStartTime = Date()
    
    // Kullanıcı davranışına göre kişiselleştirme
    @AppStorage("gamesPlayed") private var gamesPlayed = 0
    @AppStorage("favoriteCategory") private var favoriteCategory = ""
    @AppStorage("averageGameDuration") private var averageGameDuration = 30
    
    private var personalizedMessage: String {
        if gamesPlayed > 10 {
            return NSLocalizedString("experienced_player_message", comment: "")
        } else if gamesPlayed > 5 {
            return NSLocalizedString("regular_player_message", comment: "")
        } else {
            return NSLocalizedString("new_player_message", comment: "")
        }
    }
    
    private var personalizedOffer: String {
        if gamesPlayed > 15 {
            return NSLocalizedString("loyal_user_offer", comment: "")
        } else {
            return NSLocalizedString("standard_offer", comment: "")
        }
    }
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3),
                    Color(red: 0.2, green: 0.1, blue: 0.4),
                    Color(red: 0.3, green: 0.2, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 0) {
                    // Header section
                    VStack(spacing: 20) {
                        // Close button
                        HStack {
                            Spacer()
                            Button(action: { 
                                logDismissal()
                                dismiss() 
                            }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.8))
                                    .padding()
                                    .background(Color.white.opacity(0.1))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.horizontal)
                        
                        // Personalized greeting
                        VStack(spacing: 16) {
                            Text("👋")
                                .font(.system(size: 60))
                                .scaleEffect(animateContent ? 1.0 : 0.8)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateContent)
                            
                            Text(NSLocalizedString("personalized_greeting", comment: ""))
                                .font(.largeTitle.bold())
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.2), value: animateContent)
                            
                            Text(personalizedMessage)
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.3), value: animateContent)
                        }
                        .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 30)
                    
                    // User stats
                    VStack(spacing: 20) {
                        Text("📊 " + NSLocalizedString("your_stats", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        HStack(spacing: 20) {
                            StatCard(
                                title: NSLocalizedString("games_played", comment: ""),
                                value: "\(gamesPlayed)",
                                icon: "gamecontroller.fill",
                                color: .blue
                            )
                            
                            StatCard(
                                title: NSLocalizedString("favorite_category", comment: ""),
                                value: favoriteCategory.isEmpty ? NSLocalizedString("none_yet", comment: "") : favoriteCategory,
                                icon: "heart.fill",
                                color: .red
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Personalized features
                    VStack(spacing: 20) {
                        Text("✨ " + NSLocalizedString("perfect_for_you", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        VStack(spacing: 16) {
                            if gamesPlayed > 5 {
                                PremiumFeatureCard(
                                    icon: "star.fill",
                                    title: NSLocalizedString("advanced_categories", comment: ""),
                                    description: NSLocalizedString("advanced_categories_desc", comment: ""),
                                    gradient: [Color.yellow, Color.orange]
                                )
                            }
                            
                            PremiumFeatureCard(
                                icon: "xmark.circle.fill",
                                title: NSLocalizedString("no_ads", comment: ""),
                                description: NSLocalizedString("no_ads_description", comment: ""),
                                gradient: [Color.red, Color.pink]
                            )
                            
                            PremiumFeatureCard(
                                icon: "lock.open.fill",
                                title: NSLocalizedString("all_categories", comment: ""),
                                description: NSLocalizedString("all_categories_description", comment: ""),
                                gradient: [Color.green, Color.mint]
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Personalized offer
                    if let subscription = subscriptionManager.availableSubscriptions.first {
                        VStack(spacing: 20) {
                            Text("🎁 " + personalizedOffer)
                                .font(.headline.bold())
                                .foregroundColor(.yellow)
                                .multilineTextAlignment(.center)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.yellow.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.yellow.opacity(0.5), lineWidth: 1)
                                        )
                                )
                            
                            VStack(spacing: 8) {
                                Text("\(variantManager.trialDurationDays) " + NSLocalizedString("days_free", comment: ""))
                                    .font(.title.bold())
                                    .foregroundColor(.yellow)
                                
                                Text("Then \(subscription.displayPrice)\(NSLocalizedString("per_month", comment: ""))")
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                                
                                Text(NSLocalizedString("cancel_anytime", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.white.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }
                    
                    // CTA Button
                    VStack(spacing: 15) {
                        Button(action: {
                            purchaseAction()
                        }) {
                            HStack {
                                if subscriptionManager.purchaseState == .purchasing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(subscriptionManager.purchaseState == .purchasing ? NSLocalizedString("processing", comment: "") : "🚀 " + NSLocalizedString("upgrade_now", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    colors: [Color.green, Color.blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 30))
                            .shadow(color: Color.green.opacity(0.3), radius: 10, x: 0, y: 5)
                        }
                        .disabled(subscriptionManager.purchaseState == .purchasing)
                        .scaleEffect(animateContent ? 1.0 : 0.9)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.8), value: animateContent)
                        
                        Text("🎯 " + NSLocalizedString("tailored_for_you", comment: ""))
                            .font(.caption)
                            .foregroundColor(.yellow.opacity(0.8))
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                    
                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                        
                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            showStartTime = Date()
            animateContent = true
            Task {
                await subscriptionManager.loadProducts()
            }
            
            // Analytics
            AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
    }
    
    private func purchaseAction() {
        AnalyticsManager.shared.logPaywallPurchaseAttempt(variant: variantManager.getCurrentVariantForAnalytics())
        
        Task {
            await subscriptionManager.purchaseSubscription()
            if subscriptionManager.isSubscribed {
                subscriptionManager.markOfferAsShown()
                
                // Success analytics
                if let product = subscriptionManager.availableSubscriptions.first {
                    let revenue = Double(truncating: product.price as NSNumber)
                    AnalyticsManager.shared.logPaywallPurchaseSuccess(
                        variant: variantManager.getCurrentVariantForAnalytics(),
                        productId: product.id,
                        revenue: revenue
                    )
                }
                
                AnalyticsManager.shared.logPaywallTrialStarted(variant: variantManager.getCurrentVariantForAnalytics())
                dismiss()
            }
        }
    }
    
    private func logDismissal() {
        let timeSpent = Date().timeIntervalSince(showStartTime)
        AnalyticsManager.shared.logPaywallDismissed(
            variant: variantManager.getCurrentVariantForAnalytics(),
            timeSpent: timeSpent
        )
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2.bold())
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    PaywallCoordinator()
}
