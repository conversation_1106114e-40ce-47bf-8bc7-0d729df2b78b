import Foundation

enum Animals: String, CaseIterable {
    // Evcil <PERSON>
    case dog = "animals.dog"
    case cat = "animals.cat"
    case hamster = "animals.hamster"
    case rabbit = "animals.rabbit"
    case parrot = "animals.parrot"
    case goldfish = "animals.goldfish"
    case guineaPig = "animals.guinea_pig"
    
    // Vahşi <PERSON>
    case lion = "animals.lion"
    case tiger = "animals.tiger"
    case elephant = "animals.elephant"
    case giraffe = "animals.giraffe"
    case zebra = "animals.zebra"
    case monkey = "animals.monkey"
    case gorilla = "animals.gorilla"
    case cheetah = "animals.cheetah"
    case rhinoceros = "animals.rhinoceros"
    case hippopotamus = "animals.hippopotamus"
    
    // Deniz Canlıları
    case dolphin = "animals.dolphin"
    case whale = "animals.whale"
    case shark = "animals.shark"
    case octopus = "animals.octopus"
    case penguin = "animals.penguin"
    case seahorse = "animals.seahorse"
    case jellyfish = "animals.jellyfish"
    
    // Kuşlar
    case eagle = "animals.eagle"
    case owl = "animals.owl"
    case sparrow = "animals.sparrow"
    case crow = "animals.crow"
    case peacock = "animals.peacock"
    case flamingo = "animals.flamingo"
    case toucan = "animals.toucan"
    
    // Sürüngenler
    case snake = "animals.snake"
    case crocodile = "animals.crocodile"
    case turtle = "animals.turtle"
    case lizard = "animals.lizard"
    case chameleon = "animals.chameleon"
    case iguana = "animals.iguana"
    
    // Böcekler
    case butterfly = "animals.butterfly"
    case bee = "animals.bee"
    case ant = "animals.ant"
    case spider = "animals.spider"
    case grasshopper = "animals.grasshopper"
    case ladybug = "animals.ladybug"
    
    // Çiftlik Hayvanları
    case cow = "animals.cow"
    case sheep = "animals.sheep"
    case goat = "animals.goat"
    case pig = "animals.pig"
    case horse = "animals.horse"
    case chicken = "animals.chicken"
    case duck = "animals.duck"
    
    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
} 

let animalsWords = Animals.allCases.map { $0.localizedName }
