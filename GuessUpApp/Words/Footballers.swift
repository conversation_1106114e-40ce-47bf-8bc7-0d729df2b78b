import Foundation

enum Footballers: String, CaseIterable {
    case lionel<PERSON>essi = "footballers.lionel_messi"
    case cristianoRonaldo = "footballers.cristiano_ronaldo"
    case pele = "footballers.pele"
    case diegoMaradona = "footballers.diego_maradona"
    case johan<PERSON><PERSON><PERSON><PERSON> = "footballers.johan_cruyff"
    case zinedineZidane = "footballers.zinedine_zidane"
    case ronaldoNazario = "footballers.ronaldo_nazario"
    case ronaldinhoGaucho = "footballers.ronaldinho_gaucho"
    case michelPlatini = "footballers.michel_platini"
    case marcoVanBasten = "footballers.marco_van_basten"
    case georgeBest = "footballers.george_best"
    case garrincha = "footballers.garrincha"
    case franzBeckenbauer = "footballers.franz_beckenbauer"
    case alfredoDiStefano = "footballers.alfredo_di_stefano"
    case ferencPuskas = "footballers.ferenc_puskas"
    case paoloMaldini = "footballers.paolo_maldini"
    case andresIniesta = "footballers.andres_iniesta"
    case xaviHernandez = "footballers.xavi_hernandez"
    case robertoBaggio = "footballers.roberto_baggio"
    case luisSuarez = "footballers.luis_suarez"
    case thierryHenry = "footballers.thierry_henry"
    case eusebio = "footballers.eusebio"
    case gerdMuller = "footballers.gerd_muller"
    case kaka = "footballers.kaka"
    case lotharMatthaus = "footballers.lothar_matthaus"
    case romario = "footballers.romario"
    case davidBeckham = "footballers.david_beckham"
    case raulGonzalez = "footballers.raul_gonzalez"
    case bobbyCharlton = "footballers.bobby_charlton"
    case dennisBergkamp = "footballers.dennis_bergkamp"
    case zlatanIbrahimovic = "footballers.zlatan_ibrahimovic"
    case andreaPirlo = "footballers.andrea_pirlo"
    case frankLampard = "footballers.frank_lampard"
    case stevenGerrard = "footballers.steven_gerrard"
    case wayneRooney = "footballers.wayne_rooney"
    case didierDrogba = "footballers.didier_drogba"
    case samuelEtoo = "footballers.samuel_etoo"
    case ryanGiggs = "footballers.ryan_giggs"
    case georgeWeah = "footballers.george_weah"
    case levYashin = "footballers.lev_yashin"
    case javierZanetti = "footballers.javier_zanetti"
    case dinoZoff = "footballers.dino_zoff"
    case ruudGullit = "footballers.ruud_gullit"
    case robertoCarlos = "footballers.roberto_carlos"
    case cafu = "footballers.cafu"
    case francescoTotti = "footballers.francesco_totti"
    case claudioCaniggia = "footballers.claudio_caniggia"
    case michaelLaudrup = "footballers.michael_laudrup"
    case hristoStoichkov = "footballers.hristo_stoichkov"
    case paulScholes = "footballers.paul_scholes"
    case gianluigiBuffon = "footballers.gianluigi_buffon"
    case manuelNeuer = "footballers.manuel_neuer"
    case philippLahm = "footballers.philipp_lahm"
    case sergioRamos = "footballers.sergio_ramos"
    case carlesPuyol = "footballers.carles_puyol"
    case alessandroDelPiero = "footballers.alessandro_del_piero"
    case robertLewandowski = "footballers.robert_lewandowski"
    case lukaModric = "footballers.luka_modric"
    case sandroMazzola = "footballers.sandro_mazzola"
    case juanRomanRiquelme = "footballers.juan_roman_riquelme"
    case socrates = "footballers.socrates"
    case kennyDalglish = "footballers.kenny_dalglish"
    case jairzinho = "footballers.jairzinho"
    case neymarJr = "footballers.neymar_jr"
    case sadioMane = "footballers.sadio_mane"
    case mohamedSalah = "footballers.mohamed_salah"
    case kylianMbappe = "footballers.kylian_mbappe"
    case karimBenzema = "footballers.karim_benzema"
    case paulPogba = "footballers.paul_pogba"
    case rivaldo = "footballers.rivaldo"
    case davidVilla = "footballers.david_villa"
    case cescFabregas = "footballers.cesc_fabregas"
    case mesutOzil = "footballers.mesut_ozil"
    case pavelNedved = "footballers.pavel_nedved"
    case patrickVieira = "footballers.patrick_vieira"
    case claudeMakelele = "footballers.claude_makelele"
    case fernandoTorres = "footballers.fernando_torres"
    case gheorgheHagi = "footballers.gheorghe_hagi"
    case davorSuker = "footballers.davor_suker"
    case antonioDiNatale = "footballers.antonio_di_natale"
    case alessandroNesta = "footballers.alessandro_nesta"
    case edinsonCavani = "footballers.edinson_cavani"
    case jurgenKlinsmann = "footballers.jurgen_klinsmann"
    case arjenRobben = "footballers.arjen_robben"
    case bastianSchweinsteiger = "footballers.bastian_schweinsteiger"
    case hugoSanchez = "footballers.hugo_sanchez"
    case garyLineker = "footballers.gary_lineker"
    case alanShearer = "footballers.alan_shearer"
    case gonzaloHiguain = "footballers.gonzalo_higuain"
    case davidSilva = "footballers.david_silva"
    case ericCantona = "footballers.eric_cantona"
    case christianVieri = "footballers.christian_vieri"
    case miroslavKlose = "footballers.miroslav_klose"
    case ngoloKante = "footballers.ngolo_kante"
    case toniKroos = "footballers.toni_kroos"
    case garethBale = "footballers.gareth_bale"
    case diegoForlan = "footballers.diego_forlan"
    case marceloVieira = "footballers.marcelo_vieira"
    case xabiAlonso = "footballers.xabi_alonso"
    case ivanRakitic = "footballers.ivan_rakitic"
    case angelDiMaria = "footballers.angel_di_maria"
    case patrickKluivert = "footballers.patrick_kluivert"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let footballersWords = Footballers.allCases.map { $0.localizedName }
