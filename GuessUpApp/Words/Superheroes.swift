import Foundation

enum Superheroes: String, CaseIterable {
    // Marvel kahramanları
    case spiderMan = "superheroes.spider_man"
    case ironMan = "superheroes.iron_man"
    case captainAmerica = "superheroes.captain_america"
    case thor = "superheroes.thor"
    case hulk = "superheroes.hulk"
    case blackWidow = "superheroes.black_widow"
    case hawkeye = "superheroes.hawkeye"
    case doctorStrange = "superheroes.doctor_strange"
    case blackPanther = "superheroes.black_panther"
    case scarletWitch = "superheroes.scarlet_witch"
    case vision = "superheroes.vision"
    case antMan = "superheroes.ant_man"
    case wasp = "superheroes.wasp"
    case deadpool = "superheroes.deadpool"
    case wolverine = "superheroes.wolverine"
    case cyclops = "superheroes.cyclops"
    case storm = "superheroes.storm"
    case jeanGrey = "superheroes.jean_grey"
    case professorX = "superheroes.professor_x"
    case beast = "superheroes.beast"
    case gambit = "superheroes.gambit"
    case rogue = "superheroes.rogue"
    case iceman = "superheroes.iceman"
    case colossus = "superheroes.colossus"
    case nightcrawler = "superheroes.nightcrawler"
    case silverSurfer = "superheroes.silver_surfer"
    case daredevil = "superheroes.daredevil"
    case lukeCage = "superheroes.luke_cage"
    case ironFist = "superheroes.iron_fist"
    case ghostRider = "superheroes.ghost_rider"
    case moonKnight = "superheroes.moon_knight"
    case shangChi = "superheroes.shang_chi"
    case thePunisher = "superheroes.the_punisher"
    case captainMarvel = "superheroes.captain_marvel"
    case nova = "superheroes.nova"
    case starLord = "superheroes.star_lord"
    case gamora = "superheroes.gamora"
    case drax = "superheroes.drax"
    case rocketRaccoon = "superheroes.rocket_raccoon"
    case groot = "superheroes.groot"
    case nickFury = "superheroes.nick_fury"
    case quicksilver = "superheroes.quicksilver"
    case sheHulk = "superheroes.she_hulk"
    case msMarvel = "superheroes.ms_marvel"
    case sentry = "superheroes.sentry"
    case blueMarvel = "superheroes.blue_marvel"

    // DC kahramanları
    case superman = "superheroes.superman"
    case batman = "superheroes.batman"
    case wonderWoman = "superheroes.wonder_woman"
    case theFlash = "superheroes.the_flash"
    case aquaman = "superheroes.aquaman"
    case greenLantern = "superheroes.green_lantern"
    case martianManhunter = "superheroes.martian_manhunter"
    case cyborg = "superheroes.cyborg"
    case greenArrow = "superheroes.green_arrow"
    case blackCanary = "superheroes.black_canary"
    case hawkman = "superheroes.hawkman"
    case hawkgirl = "superheroes.hawkgirl"
    case shazam = "superheroes.shazam"
    case zatanna = "superheroes.zatanna"
    case johnConstantine = "superheroes.john_constantine"
    case doctorFate = "superheroes.doctor_fate"
    case raven = "superheroes.raven"
    case starfire = "superheroes.starfire"
    case beastBoy = "superheroes.beast_boy"
    case nightwing = "superheroes.nightwing"
    case robin = "superheroes.robin"
    case batgirl = "superheroes.batgirl"
    case supergirl = "superheroes.supergirl"
    case redHood = "superheroes.red_hood"
    case blueBeetle = "superheroes.blue_beetle"
    case boosterGold = "superheroes.booster_gold"
    case plasticMan = "superheroes.plastic_man"
    case theAtom = "superheroes.the_atom"
    case firestorm = "superheroes.firestorm"
    case swampThing = "superheroes.swamp_thing"
    case etriganTheDemon = "superheroes.etrigan_the_demon"
    case vixen = "superheroes.vixen"
    case blackLightning = "superheroes.black_lightning"
    case orion = "superheroes.orion"
    case bigBarda = "superheroes.big_barda"
    case misterMiracle = "superheroes.mister_miracle"
    case spectre = "superheroes.spectre"
    case doctorManhattan = "superheroes.doctor_manhattan"
    case rorschach = "superheroes.rorschach"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let superheroesWords = Superheroes.allCases.map { $0.localizedName }
