import Foundation

enum TurkishCelebrities: String, CaseIterable {
    case tarkan = "turkish_celebrities.tarkan"
    case ajdaPekkan = "turkish_celebrities.ajda_pekkan"
    case asikVeysel = "turkish_celebrities.asik_veysel"
    case barisManco = "turkish_celebrities.baris_manco"
    case bulentErsoy = "turkish_celebrities.bulent_ersoy"
    case cemKaraca = "turkish_celebrities.cem_karaca"
    case edipAkbayram = "turkish_celebrities.edip_akbayram"
    case emelSayin = "turkish_celebrities.emel_sayin"
    case erolEvgin = "turkish_celebrities.erol_evgin"
    case fazilSay = "turkish_celebrities.fazil_say"
    case ferdiTayfur = "turkish_celebrities.ferdi_tayfur"
    case gulbenErgen = "turkish_celebrities.gulben_ergen"
    case gulsinOnay = "turkish_celebrities.gulsin_onay"
    case humeyra = "turkish_celebrities.humeyra"
    case ibrahimTatlises = "turkish_celebrities.ibrahim_tatlises"
    case idilBiret = "turkish_celebrities.idil_biret"
    case kenanDogulu = "turkish_celebrities.kenan_dogulu"
    case mabelMatiz = "turkish_celebrities.mabel_matiz"
    case mazharAlanson = "turkish_celebrities.mazhar_alanson"
    case mfo = "turkish_celebrities.mfo"
    case muslumGurses = "turkish_celebrities.muslum_gurses"
    case nazanOncel = "turkish_celebrities.nazan_oncel"
    case nesetErtas = "turkish_celebrities.neset_ertas"
    case nilufer = "turkish_celebrities.nilufer"
    case orhanGencebay = "turkish_celebrities.orhan_gencebay"
    case sezenAksu = "turkish_celebrities.sezen_aksu"
    case sertabErener = "turkish_celebrities.sertab_erener"
    case teoman = "turkish_celebrities.teoman"
    case yildizTilbe = "turkish_celebrities.yildiz_tilbe"
    case zekiMuren = "turkish_celebrities.zeki_muren"
    case handeYener = "turkish_celebrities.hande_yener"
    case muratBoz = "turkish_celebrities.murat_boz"
    case gokhanTurkmen = "turkish_celebrities.gokhan_turkmen"
    case sila = "turkish_celebrities.sila"
    case edis = "turkish_celebrities.edis"
    case fundaArar = "turkish_celebrities.funda_arar"
    case demetAkalın = "turkish_celebrities.demet_akalin"
    case gulsen = "turkish_celebrities.gulsen"
    case mustafaSandal = "turkish_celebrities.mustafa_sandal"
    case gokhanOzen = "turkish_celebrities.gokhan_ozen"
    case sebnemFerah = "turkish_celebrities.sebnem_ferah"
    case morVeOtesi = "turkish_celebrities.mor_ve_otesi"
    case halukLevent = "turkish_celebrities.haluk_levent"
    case athena = "turkish_celebrities.athena"
    case manga = "turkish_celebrities.manga"
    case candanErcetin = "turkish_celebrities.candan_ercetin"
    case leventYuksel = "turkish_celebrities.levent_yuksel"
    case cemAdrian = "turkish_celebrities.cem_adrian"
    case volkanKonak = "turkish_celebrities.volkan_konak"
    case sibelCan = "turkish_celebrities.sibel_can"
    case senerSen = "turkish_celebrities.sener_sen"
    case kemalSunal = "turkish_celebrities.kemal_sunal"
    case adileNasit = "turkish_celebrities.adile_nasit"
    case munirOzkul = "turkish_celebrities.munir_ozkul"
    case halitErgenc = "turkish_celebrities.halit_ergenc"
    case kivancTatlitug = "turkish_celebrities.kivanc_tatlitug"
    case kenanImirzalioglu = "turkish_celebrities.kenan_imirzalioglu"
    case berenSaat = "turkish_celebrities.beren_saat"
    case tubaBuyukustun = "turkish_celebrities.tuba_buyukustun"
    case halukBilginer = "turkish_celebrities.haluk_bilginer"
    case tarikAkan = "turkish_celebrities.tarik_akan"
    case cuneytArkin = "turkish_celebrities.cuneyt_arkin"
    case turkanSoray = "turkish_celebrities.turkan_soray"
    case fatmaGirik = "turkish_celebrities.fatma_girik"
    case hulyaKocyigit = "turkish_celebrities.hulya_kocyigit"
    case filizAkin = "turkish_celebrities.filiz_akin"
    case nebahatCehr = "turkish_celebrities.nebahat_cehr"
    case yilmazErdogan = "turkish_celebrities.yilmaz_erdogan"
    case demetEvgar = "turkish_celebrities.demet_evgar"
    case ataDemirer = "turkish_celebrities.ata_demirer"
    case gulseBirsel = "turkish_celebrities.gulse_birsel"
    case enginAkyurek = "turkish_celebrities.engin_akyurek"
    case berguzarKorel = "turkish_celebrities.berguzar_korel"
    case burakOzcivit = "turkish_celebrities.burak_ozcivit"
    case barisArduc = "turkish_celebrities.baris_arduc"
    case serenaySarikaya = "turkish_celebrities.serenay_sarikaya"
    case halilErgun = "turkish_celebrities.halil_ergun"
    case fikretKuskan = "turkish_celebrities.fikret_kuskan"
    case erdalBesikcioglu = "turkish_celebrities.erdal_besikcioglu"
    case rizaKocaoglu = "turkish_celebrities.riza_kocaoglu"
    case ugurYucel = "turkish_celebrities.ugur_yucel"
    case meltemCumbul = "turkish_celebrities.meltem_cumbul"
    case nurgulYesilcay = "turkish_celebrities.nurgul_yesilcay"
    case ozgeOzpirinci = "turkish_celebrities.ozge_ozpirinci"
    case cansuDere = "turkish_celebrities.cansu_dere"
    case tamerKaradagli = "turkish_celebrities.tamer_karadagli"
    case selcukYontem = "turkish_celebrities.selcuk_yontem"
    case okanBayulgen = "turkish_celebrities.okan_bayulgen"
    case gurgenOz = "turkish_celebrities.gurgen_oz"
    case ismailHacioglu = "turkish_celebrities.ismail_hacioglu"
    case cagatayUlusoy = "turkish_celebrities.cagatay_ulusoy"
    case farahZeynepAbdullah = "turkish_celebrities.farah_zeynep_abdullah"
    case fundaEryigit = "turkish_celebrities.funda_eryigit"
    case mehmetGunsur = "turkish_celebrities.mehmet_gunsur"
    case yetkinDikinciler = "turkish_celebrities.yetkin_dikinciler"
    case ahmetMumtazTaylan = "turkish_celebrities.ahmet_mumtaz_taylan"
    case sumruYavrucuk = "turkish_celebrities.sumru_yavrucuk"
    case cemYilmaz = "turkish_celebrities.cem_yilmaz"
    case tolgaCevik = "turkish_celebrities.tolga_cevik"
    case yilmazGuney = "turkish_celebrities.yilmaz_guney"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let turkishCelebritiesWords = TurkishCelebrities.allCases.map { $0.localizedName }
