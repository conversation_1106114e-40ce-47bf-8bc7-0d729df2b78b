import Foundation

enum NetflixShows: String, CaseIterable {
    // Popüler Netflix Orijinalleri
    case strangerThings = "netflix_shows.stranger_things"
    case moneyHeist = "netflix_shows.money_heist"
    case theCrown = "netflix_shows.the_crown"
    case bridgerton = "netflix_shows.bridgerton"
    case theWitcher = "netflix_shows.the_witcher"
    case ozark = "netflix_shows.ozark"
    case houseOfCards = "netflix_shows.house_of_cards"
    case orangeIsTheNewBlack = "netflix_shows.orange_is_the_new_black"
    case blackMirror = "netflix_shows.black_mirror"
    case theUmbrellaAcademy = "netflix_shows.the_umbrella_academy"
    
    // Uluslararası Hitler
    case squidGame = "netflix_shows.squid_game"
    case dark = "netflix_shows.dark"
    case elitE = "netflix_shows.elite"
    case moneyHeistKorea = "netflix_shows.money_heist_korea"
    case lupin = "netflix_shows.lupin"
    case narcos = "netflix_shows.narcos"
    case narcosMexico = "netflix_shows.narcos_mexico"
    case mindhunter = "netflix_shows.mindhunter"
    case theQueensGambit = "netflix_shows.the_queens_gambit"
    case unorthodox = "netflix_shows.unorthodox"
    
    // Komedi Dizileri
    case theOffice = "netflix_shows.the_office"
    case friends = "netflix_shows.friends"
    case brooklynNineNine = "netflix_shows.brooklyn_nine_nine"
    case theGoodPlace = "netflix_shows.the_good_place"
    case schittsCreek = "netflix_shows.schitts_creek"
    case russianDoll = "netflix_shows.russian_doll"
    case sexEducation = "netflix_shows.sex_education"
    case neverHaveIEver = "netflix_shows.never_have_i_ever"
    case spaceForce = "netflix_shows.space_force"
    case theKominskyMethod = "netflix_shows.the_kominsky_method"
    
    // Anime ve Animasyon
    case deathNote = "netflix_shows.death_note"
    case attackOnTitan = "netflix_shows.attack_on_titan"
    case onePiece = "netflix_shows.one_piece"
    case naruto = "netflix_shows.naruto"
    case demonSlayer = "netflix_shows.demon_slayer"
    case myHeroAcademia = "netflix_shows.my_hero_academia"
    case jujutsuKaisen = "netflix_shows.jujutsu_kaisen"
    case hunterXHunter = "netflix_shows.hunter_x_hunter"
    case fullmetalAlchemist = "netflix_shows.fullmetal_alchemist"
    case onePartyMan = "netflix_shows.one_punch_man"
    
    // Aksiyon ve Gerilim
    case theBlacklist = "netflix_shows.the_blacklist"
    case breakingBad = "netflix_shows.breaking_bad"
    case betterCallSaul = "netflix_shows.better_call_saul"
    case peakyBlinders = "netflix_shows.peaky_blinders"
    case theWalkingDead = "netflix_shows.the_walking_dead"
    case lost = "netflix_shows.lost"
    case prison_break = "netflix_shows.prison_break"
    case suits = "netflix_shows.suits"
    case sherlock = "netflix_shows.sherlock"
    case lucifer = "netflix_shows.lucifer"
    
    // Belgesel ve Gerçek Hikayeler
    case tigerKing = "netflix_shows.tiger_king"
    case makingAMurderer = "netflix_shows.making_a_murderer"
    case wildWildCountry = "netflix_shows.wild_wild_country"
    case theKeepers = "netflix_shows.the_keepers"
    case evilGenius = "netflix_shows.evil_genius"
    case theStaircase = "netflix_shows.the_staircase"
    case americanMurder = "netflix_shows.american_murder"
    case dontFWithCats = "netflix_shows.dont_f_with_cats"
    case theDisappearanceOfMadeleineMccann = "netflix_shows.the_disappearance_of_madeleine_mccann"
    case ourPlanet = "netflix_shows.our_planet"
    
    // Türk İçerikleri
    case hakanMuhafiz = "netflix_shows.hakan_muhafiz"
    case atiye = "netflix_shows.atiye"
    case birBaskadir = "netflix_shows.bir_baskadir"
    case fatmaGul = "netflix_shows.fatma_gul"
    case theGift = "netflix_shows.the_gift"
    case theProtector = "netflix_shows.the_protector"
    case ethos = "netflix_shows.ethos"
    case midnight = "netflix_shows.midnight"
    case kulup = "netflix_shows.kulup"
    case pera_palas = "netflix_shows.pera_palas"
    
    // Bilim Kurgu ve Fantastik
    case blackMirrorBandersnatch = "netflix_shows.black_mirror_bandersnatch"
    case altered_carbon = "netflix_shows.altered_carbon"
    case theOA = "netflix_shows.the_oa"
    case sense8 = "netflix_shows.sense8"
    case lostInSpace = "netflix_shows.lost_in_space"
    case theRain = "netflix_shows.the_rain"
    case tribes_of_europa = "netflix_shows.tribes_of_europa"
    case theOrder = "netflix_shows.the_order"
    case lockeAndKey = "netflix_shows.locke_and_key"
    case theChillingAdventuresOfSabrina = "netflix_shows.the_chilling_adventures_of_sabrina"
    
    // Drama ve Romantik
    case thisIsUs = "netflix_shows.this_is_us"
    case greys_anatomy = "netflix_shows.greys_anatomy"
    case virgin_river = "netflix_shows.virgin_river"
    case outlander = "netflix_shows.outlander"
    case theGoodDoctor = "netflix_shows.the_good_doctor"
    case newGirl = "netflix_shows.new_girl"
    case howToGetAwayWithMurder = "netflix_shows.how_to_get_away_with_murder"
    case scandal = "netflix_shows.scandal"
    case riverdale = "netflix_shows.riverdale"
    case thirteenReasonsWhy = "netflix_shows.thirteen_reasons_why"
    
    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
}

let netflixShowsWords = NetflixShows.allCases.map { $0.localizedName }
