import Foundation

struct WorldCuisineCategory: WordCategory {
    let category: String = NSLocalizedString("world_cuisine.category", comment: "")
    let items: [String] = WorldCuisine.allCases.map { $0.localizedName }
}

struct TurkishCuisineCategory: WordCategory {
    let category: String = NSLocalizedString("turkish_cuisine.category", comment: "")
    let items: [String] = TurkishCuisine.allCases.map { $0.localizedName }
}

struct TurkishCelebritiesCategory: WordCategory {
    let category: String = NSLocalizedString("turkish_celebrities.category", comment: "")
    let items: [String] = TurkishCelebrities.allCases.map { $0.localizedName }
}

struct WorldCitiesCategory: WordCategory {
    let category: String = NSLocalizedString("world_cities.category", comment: "")
    let items: [String] = WorldCities.allCases.map { $0.localizedName }
}

struct SuperheroesCategory: WordCategory {
    let category: String = NSLocalizedString("superheroes.category", comment: "")
    let items: [String] = Superheroes.allCases.map { $0.localizedName }
}

struct FootballersCategory: WordCategory {
    let category: String = NSLocalizedString("footballers.category", comment: "")
    let items: [String] = Footballers.allCases.map { $0.localizedName }
}

struct HistoricalFiguresCategory: WordCategory {
    let category: String = NSLocalizedString("historical_figures.category", comment: "")
    let items: [String] = HistoricalFigures.allCases.map { $0.localizedName }
}

struct WorldMusiciansCategory: WordCategory {
    let category: String = NSLocalizedString("world_musicians.category", comment: "")
    let items: [String] = WorldMusicians.allCases.map { $0.localizedName }
}

struct WorldActorsCategory: WordCategory {
    let category: String = NSLocalizedString("world_actors.category", comment: "")
    let items: [String] = WorldActors.allCases.map { $0.localizedName }
}

struct AnimalsCategory: WordCategory {
    let category: String = NSLocalizedString("animals.category", comment: "")
    let items: [String] = Animals.allCases.map { $0.localizedName }
}

struct BollywoodCelebritiesCategory: WordCategory {
    let category: String = NSLocalizedString("bollywood_celebrities.category", comment: "")
    let items: [String] = BollywoodCelebrities.allCases.map { $0.localizedName }
}

struct TechBrandsCategory: WordCategory {
    let category: String = NSLocalizedString("tech_brands.category", comment: "")
    let items: [String] = TechBrands.allCases.map { $0.localizedName }
}

struct NetflixShowsCategory: WordCategory {
    let category: String = NSLocalizedString("netflix_shows.category", comment: "")
    let items: [String] = NetflixShows.allCases.map { $0.localizedName }
}