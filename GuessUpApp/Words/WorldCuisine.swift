import Foundation

enum WorldCuisine: String, CaseIterable {
    // <PERSON><PERSON><PERSON>
    case pizza = "world_cuisine.pizza"
    case pastaCarbonara = "world_cuisine.pasta_carbonara"
    case lasagna = "world_cuisine.lasagna"
    case risotto = "world_cuisine.risotto"
    case tiramisu = "world_cuisine.tiramisu"
    case bruschetta = "world_cuisine.bruschetta"
    case gelato = "world_cuisine.gelato"
    
    // Fransız Mutfağı
    case croissant = "world_cuisine.croissant"
    case coqAuVin = "world_cuisine.coq_au_vin"
    case ratatouille = "world_cuisine.ratatouille"
    case cremeBrulee = "world_cuisine.creme_brulee"
    case bouillabaisse = "world_cuisine.bouillabaisse"
    case frenchOnionSoup = "world_cuisine.french_onion_soup"
    case macaron = "world_cuisine.macaron"
    
    // Amerikan Mutfağı
    case burger = "world_cuisine.burger"
    case hotDog = "world_cuisine.hot_dog"
    case bbqRibs = "world_cuisine.bbq_ribs"
    case buffaloWings = "world_cuisine.buffalo_wings"
    case applePie = "world_cuisine.apple_pie"
    case macAndCheese = "world_cuisine.mac_and_cheese"
    case cheesecake = "world_cuisine.cheesecake"
    
    // Türk Mutfağı
    case kebap = "world_cuisine.kebap"
    case doner = "world_cuisine.doner"
    case baklava = "world_cuisine.baklava"
    case lahmacun = "world_cuisine.lahmacun"
    case manti = "world_cuisine.manti"
    case pide = "world_cuisine.pide"
    case karniyarik = "world_cuisine.karniyarik"
    
    // Çin Mutfağı
    case pekingDuck = "world_cuisine.peking_duck"
    case sweetAndSourPork = "world_cuisine.sweet_and_sour_pork"
    case dumplings = "world_cuisine.dumplings"
    case springRolls = "world_cuisine.spring_rolls"
    case kungPaoChicken = "world_cuisine.kung_pao_chicken"
    case friedRice = "world_cuisine.fried_rice"
    case hotPot = "world_cuisine.hot_pot"
    
    // Japon Mutfağı
    case sushi = "world_cuisine.sushi"
    case ramen = "world_cuisine.ramen"
    case tempura = "world_cuisine.tempura"
    case yakitori = "world_cuisine.yakitori"
    case misoSoup = "world_cuisine.miso_soup"
    case takoyaki = "world_cuisine.takoyaki"
    case okonomiyaki = "world_cuisine.okonomiyaki"
    
    // Hint Mutfağı
    case chickenTikkaMasala = "world_cuisine.chicken_tikka_masala"
    case biryani = "world_cuisine.biryani"
    case naan = "world_cuisine.naan"
    case dalTadka = "world_cuisine.dal_tadka"
    case butterChicken = "world_cuisine.butter_chicken"
    case samosa = "world_cuisine.samosa"
    case chanaMasala = "world_cuisine.chana_masala"
    
    // İspanyol Mutfağı
    case paella = "world_cuisine.paella"
    case tapas = "world_cuisine.tapas"
    case gazpacho = "world_cuisine.gazpacho"
    case churros = "world_cuisine.churros"
    case tortillaEspanola = "world_cuisine.tortilla_espanola"
    case jamonIberico = "world_cuisine.jamon_iberico"
    case pisto = "world_cuisine.pisto"
    
    // Meksika Mutfağı
    case tacos = "world_cuisine.tacos"
    case burrito = "world_cuisine.burrito"
    case quesadilla = "world_cuisine.quesadilla"
    case guacamole = "world_cuisine.guacamole"
    case chiliConCarne = "world_cuisine.chili_con_carne"
    case tamales = "world_cuisine.tamales"
    
    // Yunan Mutfağı
    case moussaka = "world_cuisine.moussaka"
    case souvlaki = "world_cuisine.souvlaki"
    case tzatziki = "world_cuisine.tzatziki"
    case dolmades = "world_cuisine.dolmades"
    case greekSalad = "world_cuisine.greek_salad"
    case spanakopita = "world_cuisine.spanakopita"
    
    // Tayland Mutfağı
    case padThai = "world_cuisine.pad_thai"
    case greenCurry = "world_cuisine.green_curry"
    case tomYumSoup = "world_cuisine.tom_yum_soup"
    case massamanCurry = "world_cuisine.massaman_curry"
    case mangoStickyRice = "world_cuisine.mango_sticky_rice"
    case somTam = "world_cuisine.som_tam"
    case satay = "world_cuisine.satay"
    
    // Kore Mutfağı
    case kimchi = "world_cuisine.kimchi"
    case bibimbap = "world_cuisine.bibimbap"
    case bulgogi = "world_cuisine.bulgogi"
    case tteokbokki = "world_cuisine.tteokbokki"
    case japchae = "world_cuisine.japchae"
    case samgyeopsal = "world_cuisine.samgyeopsal"
    case kimchiJjigae = "world_cuisine.kimchi_jjigae"
    
    // Orta Doğu Mutfağı
    case hummus = "world_cuisine.hummus"
    case falafel = "world_cuisine.falafel"
    case shawarma = "world_cuisine.shawarma"
    case babaGanoush = "world_cuisine.baba_ganoush"
    case tabbouleh = "world_cuisine.tabbouleh"
    case knafeh = "world_cuisine.knafeh"
    case mansaf = "world_cuisine.mansaf"
    
    // Alman Mutfağı
    case bratwurst = "world_cuisine.bratwurst"
    case sauerbraten = "world_cuisine.sauerbraten"
    case pretzel = "world_cuisine.pretzel"
    case schnitzel = "world_cuisine.schnitzel"
    case kartoffelsalat = "world_cuisine.kartoffelsalat"
    case currywurst = "world_cuisine.currywurst"
    case apfelstrudel = "world_cuisine.apfelstrudel"
    
    // Brezilya Mutfağı
    case feijoada = "world_cuisine.feijoada"
    case paoDeQueijo = "world_cuisine.pao_de_queijo"
    case brigadeiro = "world_cuisine.brigadeiro"
    case acaiBowl = "world_cuisine.acai_bowl"
    case moqueca = "world_cuisine.moqueca"
    case churrasco = "world_cuisine.churrasco"
    case coxinha = "world_cuisine.coxinha"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let worldCuisineWords = WorldCuisine.allCases.map { $0.localizedName }
