import Foundation

enum TurkishCuisine: String, CaseIterable {
    case kebap = "turkish_cuisine.kebap"
    case doner = "turkish_cuisine.doner"
    case iskender = "turkish_cuisine.iskender"
    case adanaKebabi = "turkish_cuisine.adana_kebabi"
    case urfaKebabi = "turkish_cuisine.urfa_kebabi"
    case lahmacun = "turkish_cuisine.lahmacun"
    case pide = "turkish_cuisine.pide"
    case borek = "turkish_cuisine.borek"
    case manti = "turkish_cuisine.manti"
    case cigKofte = "turkish_cuisine.cig_kofte"
    case karniyarik = "turkish_cuisine.karniyarik"
    case hunkarBegendi = "turkish_cuisine.hunkar_begendi"
    case imamBayildi = "turkish_cuisine.imam_bayildi"
    case zeytinyagliYaprakSarma = "turkish_cuisine.zeytinyagli_yaprak_sarma"
    case dolma = "turkish_cuisine.dolma"
    case kuruFasulye = "turkish_cuisine.kuru_fasulye"
    case pilav = "turkish_cuisine.pilav"
    case mercimekCorbasi = "turkish_cuisine.mercimek_corbasi"
    case ezogelinCorbasi = "turkish_cuisine.ezogelin_corbasi"
    case tarhanaCorbasi = "turkish_cuisine.tarhana_corbasi"
    case tavukluPilav = "turkish_cuisine.tavuklu_pilav"
    case balikEkmek = "turkish_cuisine.balik_ekmek"
    case menemen = "turkish_cuisine.menemen"
    case sucukluYumurta = "turkish_cuisine.sucuklu_yumurta"
    case gozleme = "turkish_cuisine.gozleme"
    case kasarliTost = "turkish_cuisine.kasarli_tost"
    case midyeDolma = "turkish_cuisine.midye_dolma"
    case kokorec = "turkish_cuisine.kokorec"
    case pacaCorbasi = "turkish_cuisine.paca_corbasi"
    case iskembeCorbasi = "turkish_cuisine.iskembe_corbasi"
    case tandirKebabi = "turkish_cuisine.tandir_kebabi"
    case cagKebabi = "turkish_cuisine.cag_kebabi"
    case testiKebabi = "turkish_cuisine.testi_kebabi"
    case aliNazik = "turkish_cuisine.ali_nazik"
    case beytiKebabi = "turkish_cuisine.beyti_kebabi"
    case patlicanKebabi = "turkish_cuisine.patlican_kebabi"
    case cizlakKebabi = "turkish_cuisine.cizlak_kebabi"
    case kagitKebabi = "turkish_cuisine.kagit_kebabi"
    case cigerSis = "turkish_cuisine.ciger_sis"
    case perdePilavi = "turkish_cuisine.perde_pilavi"
    case hamsiliPilav = "turkish_cuisine.hamsili_pilav"
    case tepsiKebabi = "turkish_cuisine.tepsi_kebabi"
    case kapuska = "turkish_cuisine.kapuska"
    case icliKofte = "turkish_cuisine.icli_kofte"
    case arnavutCigeri = "turkish_cuisine.arnavut_cigeri"
    case tirit = "turkish_cuisine.tirit"
    case keskek = "turkish_cuisine.keskek"
    case mihlama = "turkish_cuisine.mihlama"
    case kuzuGuvec = "turkish_cuisine.kuzu_guvec"
    case saksuka = "turkish_cuisine.saksuka"
    case zeytinyagliEnginar = "turkish_cuisine.zeytinyagli_enginar"
    case tazeFasulye = "turkish_cuisine.taze_fasulye"
    case kabakCicegiDolmasi = "turkish_cuisine.kabak_cicegi_dolmasi"
    case paziSarmasi = "turkish_cuisine.pazi_sarmasi"
    case baklava = "turkish_cuisine.baklava"
    case kunefe = "turkish_cuisine.kunefe"
    case kadayif = "turkish_cuisine.kadayif"
    case revani = "turkish_cuisine.revani"
    case sekerpare = "turkish_cuisine.sekerpare"
    case kazandibi = "turkish_cuisine.kazandibi"
    case sutlac = "turkish_cuisine.sutlac"
    case asure = "turkish_cuisine.asure"
    case lokum = "turkish_cuisine.lokum"
    case helva = "turkish_cuisine.helva"
    case tahinPekmez = "turkish_cuisine.tahin_pekmez"
    case cevizliSucuk = "turkish_cuisine.cevizli_sucuk"
    case zerde = "turkish_cuisine.zerde"
    case irmikHelvasi = "turkish_cuisine.irmik_helvasi"
    case trilece = "turkish_cuisine.trilece"
    case firinSutlac = "turkish_cuisine.firin_sutlac"
    case tavukgogsu = "turkish_cuisine.tavukgogsu"
    case kaymak = "turkish_cuisine.kaymak"
    case ayvaTatlisi = "turkish_cuisine.ayva_tatlisi"
    case gullac = "turkish_cuisine.gullac"
    case ekmekKadayifi = "turkish_cuisine.ekmek_kadayifi"
    case yogurt = "turkish_cuisine.yogurt"
    case ayran = "turkish_cuisine.ayran"
    case salgam = "turkish_cuisine.salgam"
    case boza = "turkish_cuisine.boza"
    case turkKahvesi = "turkish_cuisine.turk_kahvesi"
    case cay = "turkish_cuisine.cay"
    case salep = "turkish_cuisine.salep"
    case kusburnuCayi = "turkish_cuisine.kusburnu_cayi"
    case osmanliSerbetleri = "turkish_cuisine.osmanli_serbetleri"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let turkishCuisineWords = TurkishCuisine.allCases.map { $0.localizedName }
