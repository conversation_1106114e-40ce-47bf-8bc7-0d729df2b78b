import Foundation

enum HistoricalFigures: String, CaseIterable {
    case ataturk = "historical_figures.ataturk"
    case napoleon = "historical_figures.napoleon"
    case einstein = "historical_figures.einstein"
    case newton = "historical_figures.newton"
    case daVinci = "historical_figures.da_vinci"
    case caesar = "historical_figures.caesar"
    case cleopatra = "historical_figures.cleopatra"
    case gandhi = "historical_figures.gandhi"
    case churchill = "historical_figures.churchill"
    case lincoln = "historical_figures.lincoln"
    case washington = "historical_figures.washington"
    case franklin = "historical_figures.franklin"
    case edison = "historical_figures.edison"
    case tesla = "historical_figures.tesla"
    case curie = "historical_figures.curie"
    case galileo = "historical_figures.galileo"
    case socrates = "historical_figures.socrates"
    case plato = "historical_figures.plato"
    case aristotle = "historical_figures.aristotle"
    case confucius = "historical_figures.confucius"
    case sunTzu = "historical_figures.sun_tzu"
    case genghisKhan = "historical_figures.genghis_khan"
    case attila = "historical_figures.attila"
    case fatihSultanMehmet = "historical_figures.fatih_sultan_mehmet"
    case yavuzSultanSelim = "historical_figures.yavuz_sultan_selim"
    case kanuniSultanSuleyman = "historical_figures.kanuni_sultan_suleyman"
    case abdulhamidII = "historical_figures.abdulhamid_ii"
    case sultanAlparslan = "historical_figures.sultan_alparslan"
    case prophetMuhammad = "historical_figures.prophet_muhammad"
    case jesusChrist = "historical_figures.jesus_christ"
    case moses = "historical_figures.moses"
    case solomon = "historical_figures.solomon"
    case buddha = "historical_figures.buddha"
    case marx = "historical_figures.marx"
    case engels = "historical_figures.engels"
    case stalin = "historical_figures.stalin"
    case lenin = "historical_figures.lenin"
    case maoZedong = "historical_figures.mao_zedong"
    case cheGuevara = "historical_figures.che_guevara"
    case fidelCastro = "historical_figures.fidel_castro"
    case kennedy = "historical_figures.kennedy"
    case roosevelt = "historical_figures.roosevelt"
    case theodoreRoosevelt = "historical_figures.theodore_roosevelt"
    case obama = "historical_figures.obama"
    case mandela = "historical_figures.mandela"
    case thatcher = "historical_figures.thatcher"
    case elizabethI = "historical_figures.elizabeth_i"
    case elizabethII = "historical_figures.elizabeth_ii"
    case victoria = "historical_figures.victoria"
    case joanOfArc = "historical_figures.joan_of_arc"
    case bell = "historical_figures.bell"
    case jobs = "historical_figures.jobs"
    case gates = "historical_figures.gates"
    case musk = "historical_figures.musk"
    case bezos = "historical_figures.bezos"
    case freud = "historical_figures.freud"
    case darwin = "historical_figures.darwin"
    case mendel = "historical_figures.mendel"
    case turing = "historical_figures.turing"
    case hawking = "historical_figures.hawking"
    case pythagoras = "historical_figures.pythagoras"
    case archimedes = "historical_figures.archimedes"
    case hypatia = "historical_figures.hypatia"
    case alhazen = "historical_figures.alhazen"
    case avicenna = "historical_figures.avicenna"
    case averroes = "historical_figures.averroes"
    case rumi = "historical_figures.rumi"
    case yunusEmre = "historical_figures.yunus_emre"
    case haciBektas = "historical_figures.haci_bektas"
    case ahmetYesevi = "historical_figures.ahmet_yesevi"
    case ibnBattuta = "historical_figures.ibn_battuta"
    case marcoPolo = "historical_figures.marco_polo"
    case vespucci = "historical_figures.vespucci"
    case columbus = "historical_figures.columbus"
    case magellan = "historical_figures.magellan"
    case cook = "historical_figures.cook"
    case armstrong = "historical_figures.armstrong"
    case gagarin = "historical_figures.gagarin"
    case trotsky = "historical_figures.trotsky"
    case wilson = "historical_figures.wilson"
    case bismarck = "historical_figures.bismarck"
    case kublaiKhan = "historical_figures.kublai_khan"
    case mansaMusa = "historical_figures.mansa_musa"
    case ramsesII = "historical_figures.ramses_ii"
    case hammurabi = "historical_figures.hammurabi"
    case catherineTheGreat = "historical_figures.catherine_the_great"
    case shelley = "historical_figures.shelley"
    case shakespeare = "historical_figures.shakespeare"
    case austen = "historical_figures.austen"
    case hugo = "historical_figures.hugo"
    case dostoevsky = "historical_figures.dostoevsky"
    case tolstoy = "historical_figures.tolstoy"
    case poe = "historical_figures.poe"
    case picasso = "historical_figures.picasso"
    case vanGogh = "historical_figures.van_gogh"
    case beethoven = "historical_figures.beethoven"
    case mozart = "historical_figures.mozart"
    case bach = "historical_figures.bach"
    case kahlo = "historical_figures.kahlo"
    case warhol = "historical_figures.warhol"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let historicalFiguresWords = HistoricalFigures.allCases.map { $0.localizedName } 