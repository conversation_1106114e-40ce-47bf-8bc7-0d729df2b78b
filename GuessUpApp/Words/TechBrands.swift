import Foundation

enum TechBrands: String, CaseIterable {
    // Büyük Teknoloji Şirketleri
    case apple = "tech_brands.apple"
    case google = "tech_brands.google"
    case microsoft = "tech_brands.microsoft"
    case amazon = "tech_brands.amazon"
    case meta = "tech_brands.meta"
    case tesla = "tech_brands.tesla"
    case samsung = "tech_brands.samsung"
    case huawei = "tech_brands.huawei"
    case xiaomi = "tech_brands.xiaomi"
    case sony = "tech_brands.sony"
    
    // Sosyal Medya Platformları
    case instagram = "tech_brands.instagram"
    case tiktok = "tech_brands.tiktok"
    case youtube = "tech_brands.youtube"
    case twitter = "tech_brands.twitter"
    case linkedin = "tech_brands.linkedin"
    case snapchat = "tech_brands.snapchat"
    case pinterest = "tech_brands.pinterest"
    case reddit = "tech_brands.reddit"
    case discord = "tech_brands.discord"
    case telegram = "tech_brands.telegram"
    
    // Streaming ve Eğlence
    case netflix = "tech_brands.netflix"
    case spotify = "tech_brands.spotify"
    case disneyPlus = "tech_brands.disney_plus"
    case amazonPrime = "tech_brands.amazon_prime"
    case hulu = "tech_brands.hulu"
    case twitch = "tech_brands.twitch"
    case hboMax = "tech_brands.hbo_max"
    case appleTv = "tech_brands.apple_tv"
    
    // Oyun Platformları
    case steam = "tech_brands.steam"
    case playstation = "tech_brands.playstation"
    case xbox = "tech_brands.xbox"
    case nintendo = "tech_brands.nintendo"
    case epicGames = "tech_brands.epic_games"
    case roblox = "tech_brands.roblox"
    case minecraft = "tech_brands.minecraft"
    case fortnite = "tech_brands.fortnite"
    
    // E-ticaret ve Fintech
    case ebay = "tech_brands.ebay"
    case alibaba = "tech_brands.alibaba"
    case shopify = "tech_brands.shopify"
    case paypal = "tech_brands.paypal"
    case stripe = "tech_brands.stripe"
    case square = "tech_brands.square"
    case coinbase = "tech_brands.coinbase"
    
    // Donanım ve Çip Şirketleri
    case intel = "tech_brands.intel"
    case amd = "tech_brands.amd"
    case nvidia = "tech_brands.nvidia"
    case qualcomm = "tech_brands.qualcomm"
    case tsmc = "tech_brands.tsmc"
    case hp = "tech_brands.hp"
    case dell = "tech_brands.dell"
    case lenovo = "tech_brands.lenovo"
    
    // Yazılım ve Araçlar
    case adobe = "tech_brands.adobe"
    case zoom = "tech_brands.zoom"
    case slack = "tech_brands.slack"
    case dropbox = "tech_brands.dropbox"
    case notion = "tech_brands.notion"
    case figma = "tech_brands.figma"
    case canva = "tech_brands.canva"
    case github = "tech_brands.github"
    
    // Mobil Uygulamalar
    case whatsapp = "tech_brands.whatsapp"
    case uber = "tech_brands.uber"
    case airbnb = "tech_brands.airbnb"
    case tinder = "tech_brands.tinder"
    case duolingo = "tech_brands.duolingo"
    case shazam = "tech_brands.shazam"
    case waze = "tech_brands.waze"
    case skype = "tech_brands.skype"
    
    // Arama ve Tarayıcılar
    case chrome = "tech_brands.chrome"
    case firefox = "tech_brands.firefox"
    case safari = "tech_brands.safari"
    case edge = "tech_brands.edge"
    case opera = "tech_brands.opera"
    case duckduckgo = "tech_brands.duckduckgo"
    
    // Cloud ve Enterprise
    case aws = "tech_brands.aws"
    case googleCloud = "tech_brands.google_cloud"
    case azure = "tech_brands.azure"
    case salesforce = "tech_brands.salesforce"
    case oracle = "tech_brands.oracle"
    case ibm = "tech_brands.ibm"
    case vmware = "tech_brands.vmware"
    
    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
}

let techBrandsWords = TechBrands.allCases.map { $0.localizedName }
