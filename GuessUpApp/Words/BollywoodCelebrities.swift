import Foundation

enum BollywoodCelebrities: String, CaseIterable {
    case shah<PERSON><PERSON><PERSON><PERSON><PERSON> = "bollywood_celebrities.shah_rukh_khan"
    case amitabhBachchan = "bollywood_celebrities.amitabh_bachchan"
    case aami<PERSON><PERSON><PERSON> = "bollywood_celebrities.aamir_khan"
    case sa<PERSON><PERSON><PERSON> = "bollywood_celebrities.salman_khan"
    case aks<PERSON><PERSON><PERSON><PERSON> = "bollywood_celebrities.akshay_kumar"
    case hrithikRoshan = "bollywood_celebrities.hrithik_roshan"
    case ranbir<PERSON>ap<PERSON> = "bollywood_celebrities.ranbir_kapoor"
    case ranveerSingh = "bollywood_celebrities.ranveer_singh"
    case varunDhawan = "bollywood_celebrities.varun_dhawan"
    case arjun<PERSON><PERSON><PERSON> = "bollywood_celebrities.arjun_kapoor"
    case rajkummarRao = "bollywood_celebrities.rajkummar_rao"
    case ayushmann<PERSON><PERSON>rana = "bollywood_celebrities.ayushmann_khurrana"
    case kartik<PERSON><PERSON><PERSON> = "bollywood_celebrities.kartik_aaryan"
    case vickey<PERSON><PERSON><PERSON> = "bollywood_celebrities.vickey_kaushal"
    case shahid<PERSON><PERSON><PERSON> = "bollywood_celebrities.shahid_kapoor"
    case deep<PERSON>Paduk<PERSON> = "bollywood_celebrities.deep<PERSON>_padukone"
    case priyankaChopra = "bollywood_celebrities.priyanka_chopra"
    case katrinaKaif = "bollywood_celebrities.katrina_kaif"
    case aliaBhatt = "bollywood_celebrities.alia_bhatt"
    case kareenaKapoor = "bollywood_celebrities.kareena_kapoor"
    case anushkaSharma = "bollywood_celebrities.anushka_sharma"
    case sonamKapoor = "bollywood_celebrities.sonam_kapoor"
    case jacquelineFernandez = "bollywood_celebrities.jacqueline_fernandez"
    case shraddhaKapoor = "bollywood_celebrities.shraddha_kapoor"
    case parineetiChopra = "bollywood_celebrities.parineeti_chopra"
    case kritiSanon = "bollywood_celebrities.kriti_sanon"
    case taapseePannu = "bollywood_celebrities.taapsee_pannu"
    case bhumiPednekar = "bollywood_celebrities.bhumi_pednekar"
    case kanganaRanaut = "bollywood_celebrities.kangana_ranaut"
    case vidyaBalan = "bollywood_celebrities.vidya_balan"
    case madhuriDixit = "bollywood_celebrities.madhuri_dixit"
    case sridevi = "bollywood_celebrities.sridevi"
    case rekha = "bollywood_celebrities.rekha"
    case aishwaryaRai = "bollywood_celebrities.aishwarya_rai"
    case sushmitaSen = "bollywood_celebrities.sushmita_sen"
    case malaikaArora = "bollywood_celebrities.malaika_arora"
    case bipashaBasu = "bollywood_celebrities.bipasha_basu"
    case chitrangadaSingh = "bollywood_celebrities.chitrangada_singh"
    case nimratKaur = "bollywood_celebrities.nimrat_kaur"
    case richaChadha = "bollywood_celebrities.richa_chadha"
    case konkonaSen = "bollywood_celebrities.konkona_sen"
    case kalkiKoechlin = "bollywood_celebrities.kalki_koechlin"
    case radhikaApte = "bollywood_celebrities.radhika_apte"
    case tillotamaShome = "bollywood_celebrities.tillotama_shome"
    case nawazuddinSiddiqui = "bollywood_celebrities.nawazuddin_siddiqui"
    case irrfanKhan = "bollywood_celebrities.irrfan_khan"
    case manojBajpayee = "bollywood_celebrities.manoj_bajpayee"
    case pankajTripathi = "bollywood_celebrities.pankaj_tripathi"
    case kayKayMenon = "bollywood_celebrities.kay_kay_menon"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let bollywoodCelebritiesWords = BollywoodCelebrities.allCases.map { $0.localizedName }