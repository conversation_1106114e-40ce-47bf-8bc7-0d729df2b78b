import Foundation

enum WorldActors: String, CaseIterable {
    case leonardoDiCaprio = "world_actors.leonardo_dicaprio"
    case scarlet<PERSON><PERSON><PERSON><PERSON><PERSON> = "world_actors.scarlett_johansson"
    case dwayne<PERSON><PERSON><PERSON> = "world_actors.dwayne_johnson"
    case bradPitt = "world_actors.brad_pitt"
    case johnnyDepp = "world_actors.johnny_depp"
    case emmaStone = "world_actors.emma_stone"
    case angelinaJolie = "world_actors.angelina_jolie"
    case robertDowneyJr = "world_actors.robert_downey_jr"
    case chrisHemsworth = "world_actors.chris_hemsworth"
    case tomHanks = "world_actors.tom_hanks"
    case merylStreep = "world_actors.meryl_streep"
    case willSmith = "world_actors.will_smith"
    case jenniferLawrence = "world_actors.jennifer_lawrence"
    case keanuReeves = "world_actors.keanu_reeves"
    case tomCruise = "world_actors.tom_cruise"
    case zendaya = "world_actors.zendaya"
    case ryanReynolds = "world_actors.ryan_reynolds"
    case emmaWatson = "world_actors.emma_watson"
    case chrisEvans = "world_actors.chris_evans"
    case galGadot = "world_actors.gal_gadot"
    case samuelLJackson = "world_actors.samuel_l_jackson"
    case morganFreeman = "world_actors.morgan_freeman"
    case juliaRoberts = "world_actors.julia_roberts"
    case nicoleKidman = "world_actors.nicole_kidman"
    case matthewMcConaughey = "world_actors.matthew_mcconaughey"
    case charlizeTheron = "world_actors.charlize_theron"
    case denzelWashington = "world_actors.denzel_washington"
    case mattDamon = "world_actors.matt_damon"
    case cateBlanchett = "world_actors.cate_blanchett"
    case margotRobbie = "world_actors.margot_robbie"
    case jessicaChastain = "world_actors.jessica_chastain"
    case tomHardy = "world_actors.tom_hardy"
    case aliciaVikander = "world_actors.alicia_vikander"
    case nataliePortman = "world_actors.natalie_portman"
    case michaelFassbender = "world_actors.michael_fassbender"
    case saoirseRonan = "world_actors.saoirse_ronan"
    case reeseWitherspoon = "world_actors.reese_witherspoon"
    case jaredLeto = "world_actors.jared_leto"
    case anneHathaway = "world_actors.anne_hathaway"
    case paulRudd = "world_actors.paul_rudd"
    case chadwickBoseman = "world_actors.chadwick_boseman"
    case jasonMomoa = "world_actors.jason_momoa"
    case adamDriver = "world_actors.adam_driver"
    case ramiMalek = "world_actors.rami_malek"
    case tessaThompson = "world_actors.tessa_thompson"
    case sophieTurner = "world_actors.sophie_turner"
    case henryCavill = "world_actors.henry_cavill"
    case keiraKnightley = "world_actors.keira_knightley"
    case willFerrell = "world_actors.will_ferrell"
    case benAffleck = "world_actors.ben_affleck"
    case mattSmith = "world_actors.matt_smith"
    case caitrionaBalfe = "world_actors.caitriona_balfe"
    case kerryWashington = "world_actors.kerry_washington"
    case violaDavis = "world_actors.viola_davis"
    case markRuffalo = "world_actors.mark_ruffalo"
    case amyAdams = "world_actors.amy_adams"
    case rachelMcAdams = "world_actors.rachel_mcadams"
    case shaileneWoodley = "world_actors.shailene_woodley"
    case paulWalker = "world_actors.paul_walker"
    case johnBoyega = "world_actors.john_boyega"
    case ethanHawke = "world_actors.ethan_hawke"
    case idrisElba = "world_actors.idris_elba"
    case tinaFey = "world_actors.tina_fey"
    case steveCarell = "world_actors.steve_carell"
    case ryanGosling = "world_actors.ryan_gosling"
    case bryanCranston = "world_actors.bryan_cranston"
    case harrisonFord = "world_actors.harrison_ford"
    case danielCraig = "world_actors.daniel_craig"
    case jeremyRenner = "world_actors.jeremy_renner"
    case ginaRodriguez = "world_actors.gina_rodriguez"
    case mindyKaling = "world_actors.mindy_kaling"
    case benedictCumberbatch = "world_actors.benedict_cumberbatch"
    case chrisPratt = "world_actors.chris_pratt"
    case priyankaChopra = "world_actors.priyanka_chopra"
    case keeganMichaelKey = "world_actors.keegan_michael_key"
    case mandyMoore = "world_actors.mandy_moore"
    case hughJackman = "world_actors.hugh_jackman"
    case julianneMoore = "world_actors.julianne_moore"
    case anyaTaylorJoy = "world_actors.anya_taylor_joy"
    case tomHolland = "world_actors.tom_holland"
    case emmaRoberts = "world_actors.emma_roberts"
    case zoeSaldana = "world_actors.zoe_saldana"
    case rachaelLeighCook = "world_actors.rachael_leigh_cook"
    case jesseEisenberg = "world_actors.jesse_eisenberg"
    case michelleWilliams = "world_actors.michelle_williams"
    case andrewGarfield = "world_actors.andrew_garfield"
    case chrisPine = "world_actors.chris_pine"
    case channingTatum = "world_actors.channing_tatum"
    case tiffanyHaddish = "world_actors.tiffany_haddish"
    case lupitaNyongo = "world_actors.lupita_nyongo"
    case jasonStatham = "world_actors.jason_statham"
    case lilyJames = "world_actors.lily_james"
    case charlieHunnam = "world_actors.charlie_hunnam"
    case evanPeters = "world_actors.evan_peters"
    case tomFelton = "world_actors.tom_felton"
    case jessicaBiel = "world_actors.jessica_biel"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let worldActorsWords = WorldActors.allCases.map { $0.localizedName } 