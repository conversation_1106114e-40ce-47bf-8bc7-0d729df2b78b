import Foundation

enum WorldMusicians: String, CaseIterable {
    case beyonce = "world_musicians.beyonce"
    case adele = "world_musicians.adele"
    case taylorSwift = "world_musicians.taylor_swift"
    case edSheeran = "world_musicians.ed_sheeran"
    case drake = "world_musicians.drake"
    case kanyeWest = "world_musicians.kanye_west"
    case rihanna = "world_musicians.rihanna"
    case brunoMars = "world_musicians.bruno_mars"
    case justinBieber = "world_musicians.justin_bieber"
    case katyPerry = "world_musicians.katy_perry"
    case ladyGaga = "world_musicians.lady_gaga"
    case postMalone = "world_musicians.post_malone"
    case shakira = "world_musicians.shakira"
    case billieEilish = "world_musicians.billie_eilish"
    case travisScott = "world_musicians.travis_scott"
    case cardiB = "world_musicians.cardi_b"
    case theWeeknd = "world_musicians.the_weeknd"
    case selenaGomez = "world_musicians.selena_gomez"
    case harryStyles = "world_musicians.harry_styles"
    case lizzo = "world_musicians.lizzo"
    case mileyCyrus = "world_musicians.miley_cyrus"
    case arianaGrande = "world_musicians.ariana_grande"
    case kendrickLamar = "world_musicians.kendrick_lamar"
    case maroon5 = "world_musicians.maroon_5"
    case pink = "world_musicians.pink"
    case johnLegend = "world_musicians.john_legend"
    case coldplay = "world_musicians.coldplay"
    case sia = "world_musicians.sia"
    case samSmith = "world_musicians.sam_smith"
    case drDre = "world_musicians.dr_dre"
    case lilNasX = "world_musicians.lil_nas_x"
    case duaLipa = "world_musicians.dua_lipa"
    case camilaCabello = "world_musicians.camila_cabello"
    case shawnMendes = "world_musicians.shawn_mendes"
    case imagineDragons = "world_musicians.imagine_dragons"
    case nickiMinaj = "world_musicians.nicki_minaj"
    case migos = "world_musicians.migos"
    case future = "world_musicians.future"
    case ritaOra = "world_musicians.rita_ora"
    case jessieJ = "world_musicians.jessie_j"
    case zaynMalik = "world_musicians.zayn_malik"
    case kesha = "world_musicians.kesha"
    case pitbull = "world_musicians.pitbull"
    case lorde = "world_musicians.lorde"
    case khalid = "world_musicians.khalid"
    case jBalvin = "world_musicians.j_balvin"
    case badBunny = "world_musicians.bad_bunny"
    case jheneAiko = "world_musicians.jhene_aiko"
    case avicii = "world_musicians.avicii"
    case davidGuetta = "world_musicians.david_guetta"
    case calvinHarris = "world_musicians.calvin_harris"
    case marshmello = "world_musicians.marshmello"
    case alesso = "world_musicians.alesso"
    case flume = "world_musicians.flume"
    case kygo = "world_musicians.kygo"
    case theChainsmokers = "world_musicians.the_chainsmokers"
    case martinGarrix = "world_musicians.martin_garrix"
    case travisBarker = "world_musicians.travis_barker"
    case bts = "world_musicians.bts"
    case blackpink = "world_musicians.blackpink"
    case exo = "world_musicians.exo"
    case twice = "world_musicians.twice"
    case asapRocky = "world_musicians.asap_rocky"
    case lilWayne = "world_musicians.lil_wayne"
    case eminem = "world_musicians.eminem"
    case jayZ = "world_musicians.jay_z"
    case lanaDelRey = "world_musicians.lana_del_rey"
    case tameImpala = "world_musicians.tame_impala"
    case macklemore = "world_musicians.macklemore"
    case oneDirection = "world_musicians.one_direction"
    case marinaDiamandis = "world_musicians.marina_diamandis"
    case theKillers = "world_musicians.the_killers"
    case twentyOnePilots = "world_musicians.twenty_one_pilots"
    case arcticMonkeys = "world_musicians.arctic_monkeys"
    case halsey = "world_musicians.halsey"
    case jackHarlow = "world_musicians.jack_harlow"
    case celineDion = "world_musicians.celine_dion"
    case zedd = "world_musicians.zedd"
    case lauv = "world_musicians.lauv"
    case troyeSivan = "world_musicians.troye_sivan"
    case panicAtTheDisco = "world_musicians.panic_at_the_disco"
    case hayleyWilliams = "world_musicians.hayley_williams"
    case skrillex = "world_musicians.skrillex"
    case nickyJam = "world_musicians.nicky_jam"
    case snoopDogg = "world_musicians.snoop_dogg"
    case tylerTheCreator = "world_musicians.tyler_the_creator"
    case chrisBrown = "world_musicians.chris_brown"
    case fifthHarmony = "world_musicians.fifth_harmony"
    case aliciaKeys = "world_musicians.alicia_keys"
    case shaggy = "world_musicians.shaggy"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let worldMusiciansWords = WorldMusicians.allCases.map { $0.localizedName } 