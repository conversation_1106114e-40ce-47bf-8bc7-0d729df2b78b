import Foundation

enum WorldCities: String, CaseIterable {
    case newYork = "world_cities.new_york"
    case losAngeles = "world_cities.los_angeles"
    case london = "world_cities.london"
    case paris = "world_cities.paris"
    case rome = "world_cities.rome"
    case berlin = "world_cities.berlin"
    case madrid = "world_cities.madrid"
    case barcelona = "world_cities.barcelona"
    case istanbul = "world_cities.istanbul"
    case dubai = "world_cities.dubai"
    case tokyo = "world_cities.tokyo"
    case kyoto = "world_cities.kyoto"
    case beijing = "world_cities.beijing"
    case shanghai = "world_cities.shanghai"
    case hongKong = "world_cities.hong_kong"
    case bangkok = "world_cities.bangkok"
    case singapore = "world_cities.singapore"
    case seoul = "world_cities.seoul"
    case moscow = "world_cities.moscow"
    case stPetersburg = "world_cities.st_petersburg"
    case sydney = "world_cities.sydney"
    case melbourne = "world_cities.melbourne"
    case rioDeJaneiro = "world_cities.rio_de_janeiro"
    case saoPaulo = "world_cities.sao_paulo"
    case buenosAires = "world_cities.buenos_aires"
    case mexicoCity = "world_cities.mexico_city"
    case toronto = "world_cities.toronto"
    case vancouver = "world_cities.vancouver"
    case chicago = "world_cities.chicago"
    case sanFrancisco = "world_cities.san_francisco"
    case washingtonDc = "world_cities.washington_dc"
    case lasVegas = "world_cities.las_vegas"
    case miami = "world_cities.miami"
    case boston = "world_cities.boston"
    case amsterdam = "world_cities.amsterdam"
    case brussels = "world_cities.brussels"
    case vienna = "world_cities.vienna"
    case prague = "world_cities.prague"
    case budapest = "world_cities.budapest"
    case copenhagen = "world_cities.copenhagen"
    case stockholm = "world_cities.stockholm"
    case oslo = "world_cities.oslo"
    case helsinki = "world_cities.helsinki"
    case dublin = "world_cities.dublin"
    case athens = "world_cities.athens"
    case warsaw = "world_cities.warsaw"
    case lisbon = "world_cities.lisbon"
    case venice = "world_cities.venice"
    case milan = "world_cities.milan"
    case florence = "world_cities.florence"
    case naples = "world_cities.naples"
    case zurich = "world_cities.zurich"
    case geneva = "world_cities.geneva"
    case munich = "world_cities.munich"
    case frankfurt = "world_cities.frankfurt"
    case hamburg = "world_cities.hamburg"
    case johannesburg = "world_cities.johannesburg"
    case capeTown = "world_cities.cape_town"
    case casablanca = "world_cities.casablanca"
    case marrakech = "world_cities.marrakech"
    case ankara = "world_cities.ankara"
    case jerusalem = "world_cities.jerusalem"
    case cairo = "world_cities.cairo"
    case alexandria = "world_cities.alexandria"
    case newDelhi = "world_cities.new_delhi"
    case mumbai = "world_cities.mumbai"
    case bangalore = "world_cities.bangalore"
    case jakarta = "world_cities.jakarta"
    case kualaLumpur = "world_cities.kuala_lumpur"
    case hoChiMinhCity = "world_cities.ho_chi_minh_city"
    case hanoi = "world_cities.hanoi"
    case manila = "world_cities.manila"
    case taipei = "world_cities.taipei"
    case doha = "world_cities.doha"
    case abuDhabi = "world_cities.abu_dhabi"
    case riyadh = "world_cities.riyadh"
    case mecca = "world_cities.mecca"
    case medina = "world_cities.medina"
    case tehran = "world_cities.tehran"
    case baghdad = "world_cities.baghdad"
    case karachi = "world_cities.karachi"
    case lagos = "world_cities.lagos"
    case nairobi = "world_cities.nairobi"
    case accra = "world_cities.accra"
    case perth = "world_cities.perth"
    case brisbane = "world_cities.brisbane"
    case auckland = "world_cities.auckland"
    case wellington = "world_cities.wellington"
    case kathmandu = "world_cities.kathmandu"
    case havana = "world_cities.havana"
    case santiago = "world_cities.santiago"
    case bogotá = "world_cities.bogota"
    case lima = "world_cities.lima"
    case quito = "world_cities.quito"
    case montevideo = "world_cities.montevideo"
    case caracas = "world_cities.caracas"
    case sanJuan = "world_cities.san_juan"
    case panamaCity = "world_cities.panama_city"
    case reykjavik = "world_cities.reykjavik"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let worldCitiesWords = WorldCities.allCases.map { $0.localizedName }
