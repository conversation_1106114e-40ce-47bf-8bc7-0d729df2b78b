//
//  SubscriptionManager.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import Foundation
import StoreKit
import SwiftUI

@MainActor
class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()
    
    // MARK: - Published Properties
    @Published var isSubscribed = false
    @Published var subscriptionStatus: Product.SubscriptionInfo.Status?
    @Published var currentSubscription: Product?
    @Published var availableSubscriptions: [Product] = []
    @Published var purchaseState: PurchaseState = .idle
    
    // MARK: - Constants
    private let subscriptionProductID = "guessup_premium_monthly"
    
    // MARK: - UserDefaults Keys
    private let hasShownOfferKey = "has_shown_subscription_offer"
    private let subscriptionStatusKey = "subscription_status"
    
    // MARK: - Purchase States
    enum PurchaseState: Equatable {
        case idle
        case purchasing
        case purchased
        case failed(Error)
        case cancelled

        static func == (lhs: PurchaseState, rhs: PurchaseState) -> Bool {
            switch (lhs, rhs) {
            case (.idle, .idle), (.purchasing, .purchasing), (.purchased, .purchased), (.cancelled, .cancelled):
                return true
            case (.failed, .failed):
                return true // Simplified comparison for errors
            default:
                return false
            }
        }
    }
    
    private init() {
        // Transaction listener'ı başlat
        Task {
            await startTransactionListener()
            await loadProducts()
            await updateSubscriptionStatus()
        }
    }
    
    // MARK: - Public Methods
    
    /// Subscription ürünlerini yükle
    func loadProducts() async {
        print("🔍 Ürün yükleme başlatılıyor...")
        print("🆔 Aranan Product ID: \(subscriptionProductID)")

        do {
            let products = try await Product.products(for: [subscriptionProductID])
            print("📦 Bulunan toplam ürün sayısı: \(products.count)")

            for product in products {
                print("📱 Ürün: \(product.id) - \(product.displayName) - Type: \(product.type)")
            }

            availableSubscriptions = products.filter { $0.type == .autoRenewable }
            print("✅ Subscription ürünleri yüklendi: \(availableSubscriptions.count)")

            if availableSubscriptions.isEmpty {
                print("⚠️ Hiç subscription ürünü bulunamadı!")
                print("💡 App Store Connect'te '\(subscriptionProductID)' ID'li ürün oluşturulmuş mu?")
                print("💡 Sandbox test kullanıcısı ile giriş yapıldı mı?")
            }
        } catch {
            print("❌ Ürün yükleme hatası: \(error)")
            print("🔧 Hata detayı: \(error.localizedDescription)")
        }
    }
    
    /// Subscription satın al
    func purchaseSubscription() async {
        guard let product = availableSubscriptions.first else {
            print("Satın alınacak ürün bulunamadı")
            return
        }
        
        purchaseState = .purchasing
        
        do {
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                switch verification {
                case .verified(let transaction):
                    // Transaction doğrulandı
                    await transaction.finish()
                    await updateSubscriptionStatus()
                    purchaseState = .purchased
                    
                    // Analytics event'i gönder
                    AnalyticsManager.shared.logSubscriptionPurchase(productId: product.id)
                    
                case .unverified:
                    purchaseState = .failed(SubscriptionError.verificationFailed)
                }
                
            case .userCancelled:
                purchaseState = .cancelled
                
            case .pending:
                purchaseState = .idle
                
            @unknown default:
                purchaseState = .failed(SubscriptionError.unknownError)
            }
            
        } catch {
            purchaseState = .failed(error)
            print("Satın alma hatası: \(error)")
        }
    }
    
    /// Subscription durumunu güncelle
    func updateSubscriptionStatus() async {
        var isCurrentlySubscribed = false
        
        for await result in Transaction.currentEntitlements {
            switch result {
            case .verified(let transaction):
                if transaction.productID == subscriptionProductID {
                    isCurrentlySubscribed = true
                    
                    // Subscription bilgilerini al
                    if let product = availableSubscriptions.first(where: { $0.id == transaction.productID }) {
                        currentSubscription = product
                    }
                }
            case .unverified:
                break
            }
        }
        
        isSubscribed = isCurrentlySubscribed
        UserDefaults.standard.set(isCurrentlySubscribed, forKey: subscriptionStatusKey)
        
        print("Subscription durumu güncellendi: \(isSubscribed)")
    }
    
    /// Restore purchases
    func restorePurchases() async {
        do {
            try await AppStore.sync()
            await updateSubscriptionStatus()
        } catch {
            print("Restore purchases hatası: \(error)")
        }
    }
    
    /// Subscription manage sayfasını aç
    func manageSubscriptions() {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            Task {
                try await AppStore.showManageSubscriptions(in: windowScene)
            }
        }
    }
    
    /// Teklif sayfası gösterildi mi?
    var hasShownOffer: Bool {
        get { UserDefaults.standard.bool(forKey: hasShownOfferKey) }
        set { UserDefaults.standard.set(newValue, forKey: hasShownOfferKey) }
    }
    
    /// Teklif sayfasını gösterildi olarak işaretle
    func markOfferAsShown() {
        hasShownOffer = true
    }
    
    // MARK: - Private Methods
    
    /// Transaction listener'ı başlat
    private func startTransactionListener() async {
        for await result in Transaction.updates {
            switch result {
            case .verified(let transaction):
                await transaction.finish()
                await updateSubscriptionStatus()
            case .unverified:
                break
            }
        }
    }
}

// MARK: - Subscription Errors
enum SubscriptionError: LocalizedError {
    case verificationFailed
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .verificationFailed:
            return "Transaction verification failed"
        case .unknownError:
            return "Unknown error occurred"
        }
    }
}
