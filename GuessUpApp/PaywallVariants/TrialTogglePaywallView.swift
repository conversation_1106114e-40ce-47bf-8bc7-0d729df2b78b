//
//  TrialTogglePaywallView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import StoreKit

struct TrialTogglePaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var variantManager = PaywallVariantManager.shared
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var animateContent = false
    @State private var isTrialEnabled = true
    @State private var showStartTime = Date()
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3),
                    Color(red: 0.2, green: 0.1, blue: 0.4),
                    Color(red: 0.3, green: 0.2, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 0) {
                    // Header section
                    VStack(spacing: 20) {
                        // Close button
                        HStack {
                            Spacer()
                            Button(action: { 
                                logDismissal()
                                dismiss() 
                            }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.8))
                                    .padding()
                                    .background(Color.white.opacity(0.1))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.horizontal)
                        
                        // Premium crown icon with animation
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [Color.yellow, Color.orange],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 120)
                                .shadow(color: .yellow.opacity(0.3), radius: 20, x: 0, y: 10)
                                .scaleEffect(animateContent ? 1.0 : 0.8)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateContent)
                            
                            Image(systemName: "crown.fill")
                                .font(.system(size: 50))
                                .foregroundColor(.white)
                                .rotationEffect(.degrees(animateContent ? 0 : -10))
                                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: animateContent)
                        }
                        
                        // Title and subtitle
                        VStack(spacing: 12) {
                            Text("🎉 " + NSLocalizedString("unlock_premium", comment: ""))
                                .font(.largeTitle.bold())
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.3), value: animateContent)
                            
                            Text(NSLocalizedString("trial_toggle_subtitle", comment: ""))
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.4), value: animateContent)
                        }
                        .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 40)
                    
                    // Features section
                    VStack(spacing: 20) {
                        PremiumFeatureCard(
                            icon: "xmark.circle.fill",
                            title: NSLocalizedString("no_ads", comment: ""),
                            description: NSLocalizedString("no_ads_description", comment: ""),
                            gradient: [Color.red, Color.pink]
                        )
                        .opacity(animateContent ? 1 : 0)
                        .offset(x: animateContent ? 0 : -50)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateContent)
                        
                        PremiumFeatureCard(
                            icon: "lock.open.fill",
                            title: NSLocalizedString("all_categories", comment: ""),
                            description: NSLocalizedString("all_categories_description", comment: ""),
                            gradient: [Color.green, Color.mint]
                        )
                        .opacity(animateContent ? 1 : 0)
                        .offset(x: animateContent ? 0 : 50)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.6), value: animateContent)
                        
                        PremiumFeatureCard(
                            icon: "sparkles",
                            title: NSLocalizedString("premium_features", comment: ""),
                            description: NSLocalizedString("premium_features_description", comment: ""),
                            gradient: [Color.purple, Color.blue]
                        )
                        .opacity(animateContent ? 1 : 0)
                        .offset(x: animateContent ? 0 : -50)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.7), value: animateContent)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
                    
                    // Trial Toggle Section
                    VStack(spacing: 20) {
                        HStack {
                            VStack(alignment: .leading, spacing: 8) {
                                Text(isTrialEnabled ? NSLocalizedString("start_free_trial", comment: "") : NSLocalizedString("subscribe_now", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                                
                                if isTrialEnabled {
                                    Text("\(variantManager.trialDurationDays) " + NSLocalizedString("days_free_then", comment: "") + " \(subscriptionManager.availableSubscriptions.first?.displayPrice ?? "$4.99")" + NSLocalizedString("per_month", comment: ""))
                                        .font(.subheadline)
                                        .foregroundColor(.white.opacity(0.8))
                                } else {
                                    Text("\(subscriptionManager.availableSubscriptions.first?.displayPrice ?? "$4.99")" + NSLocalizedString("per_month", comment: ""))
                                        .font(.subheadline)
                                        .foregroundColor(.white.opacity(0.8))
                                }
                            }
                            
                            Spacer()
                            
                            Toggle("", isOn: $isTrialEnabled)
                                .toggleStyle(SwitchToggleStyle(tint: .green))
                                .scaleEffect(1.2)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                        
                        if isTrialEnabled {
                            Text("💡 " + NSLocalizedString("trial_toggle_tip", comment: ""))
                                .font(.caption)
                                .foregroundColor(.yellow)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // CTA Button
                    VStack(spacing: 15) {
                        Button(action: {
                            purchaseAction()
                        }) {
                            HStack {
                                if subscriptionManager.purchaseState == .purchasing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(getButtonText())
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    colors: isTrialEnabled ? [Color.green, Color.blue] : [Color.orange, Color.red],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 30))
                            .shadow(color: (isTrialEnabled ? Color.green : Color.orange).opacity(0.3), radius: 10, x: 0, y: 5)
                        }
                        .disabled(subscriptionManager.purchaseState == .purchasing)
                        .scaleEffect(animateContent ? 1.0 : 0.9)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.8), value: animateContent)
                        
                        Text(NSLocalizedString("cancel_anytime", comment: ""))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.6))
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                    
                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                        
                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            showStartTime = Date()
            animateContent = true
            Task {
                await subscriptionManager.loadProducts()
            }
            
            // Analytics
            AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
    }
    
    private func getButtonText() -> String {
        if subscriptionManager.purchaseState == .purchasing {
            return NSLocalizedString("processing", comment: "")
        }
        
        return isTrialEnabled ? 
            NSLocalizedString("start_free_trial", comment: "") : 
            NSLocalizedString("subscribe_now", comment: "")
    }
    
    private func purchaseAction() {
        AnalyticsManager.shared.logPaywallPurchaseAttempt(variant: variantManager.getCurrentVariantForAnalytics())
        
        Task {
            await subscriptionManager.purchaseSubscription()
            if subscriptionManager.isSubscribed {
                subscriptionManager.markOfferAsShown()
                
                // Success analytics
                if let product = subscriptionManager.availableSubscriptions.first {
                    let revenue = Double(truncating: product.price as NSNumber)
                    AnalyticsManager.shared.logPaywallPurchaseSuccess(
                        variant: variantManager.getCurrentVariantForAnalytics(),
                        productId: product.id,
                        revenue: revenue
                    )
                }
                
                if isTrialEnabled {
                    AnalyticsManager.shared.logPaywallTrialStarted(variant: variantManager.getCurrentVariantForAnalytics())
                }
                
                dismiss()
            }
        }
    }
    
    private func logDismissal() {
        let timeSpent = Date().timeIntervalSince(showStartTime)
        AnalyticsManager.shared.logPaywallDismissed(
            variant: variantManager.getCurrentVariantForAnalytics(),
            timeSpent: timeSpent
        )
    }
}

#Preview {
    TrialTogglePaywallView()
}
