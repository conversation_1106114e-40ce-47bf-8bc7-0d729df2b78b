//
//  HonestPaywallView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import StoreKit

struct HonestPaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var variantManager = PaywallVariantManager.shared
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var animateContent = false
    @State private var showStartTime = Date()
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3),
                    Color(red: 0.2, green: 0.1, blue: 0.4),
                    Color(red: 0.3, green: 0.2, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 0) {
                    // Header section
                    VStack(spacing: 20) {
                        // Close button
                        HStack {
                            Spacer()
                            Button(action: { 
                                logDismissal()
                                dismiss() 
                            }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.8))
                                    .padding()
                                    .background(Color.white.opacity(0.1))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.horizontal)
                        
                        // Honest approach header
                        VStack(spacing: 16) {
                            Text("🤝")
                                .font(.system(size: 60))
                                .scaleEffect(animateContent ? 1.0 : 0.8)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateContent)
                            
                            Text(NSLocalizedString("honest_approach", comment: ""))
                                .font(.largeTitle.bold())
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.2), value: animateContent)
                            
                            Text(NSLocalizedString("honest_subtitle", comment: ""))
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.3), value: animateContent)
                        }
                        .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 30)
                    
                    // Honest facts section
                    VStack(spacing: 20) {
                        Text("📋 " + NSLocalizedString("honest_facts", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        VStack(spacing: 16) {
                            HonestFactCard(
                                icon: "dollarsign.circle.fill",
                                title: NSLocalizedString("development_costs", comment: ""),
                                description: NSLocalizedString("development_costs_desc", comment: ""),
                                color: .green
                            )
                            
                            HonestFactCard(
                                icon: "server.rack",
                                title: NSLocalizedString("server_costs", comment: ""),
                                description: NSLocalizedString("server_costs_desc", comment: ""),
                                color: .blue
                            )
                            
                            HonestFactCard(
                                icon: "arrow.up.circle.fill",
                                title: NSLocalizedString("continuous_updates", comment: ""),
                                description: NSLocalizedString("continuous_updates_desc", comment: ""),
                                color: .purple
                            )
                            
                            HonestFactCard(
                                icon: "heart.fill",
                                title: NSLocalizedString("passion_project", comment: ""),
                                description: NSLocalizedString("passion_project_desc", comment: ""),
                                color: .red
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // What you get section
                    VStack(spacing: 20) {
                        Text("✨ " + NSLocalizedString("what_you_get", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        VStack(spacing: 16) {
                            BenefitRow(
                                icon: "xmark.circle.fill",
                                title: NSLocalizedString("no_ads", comment: ""),
                                description: NSLocalizedString("honest_no_ads_desc", comment: ""),
                                color: .red
                            )
                            
                            BenefitRow(
                                icon: "lock.open.fill",
                                title: NSLocalizedString("all_categories", comment: ""),
                                description: NSLocalizedString("honest_categories_desc", comment: ""),
                                color: .green
                            )
                            
                            BenefitRow(
                                icon: "sparkles",
                                title: NSLocalizedString("future_features", comment: ""),
                                description: NSLocalizedString("honest_future_desc", comment: ""),
                                color: .yellow
                            )
                            
                            BenefitRow(
                                icon: "message.fill",
                                title: NSLocalizedString("direct_support", comment: ""),
                                description: NSLocalizedString("honest_support_desc", comment: ""),
                                color: .blue
                            )
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Transparent pricing
                    if let subscription = subscriptionManager.availableSubscriptions.first {
                        VStack(spacing: 20) {
                            Text("💰 " + NSLocalizedString("transparent_pricing", comment: ""))
                                .font(.headline.bold())
                                .foregroundColor(.white)
                            
                            VStack(spacing: 16) {
                                // Trial info
                                HStack {
                                    Image(systemName: "calendar")
                                        .foregroundColor(.green)
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(NSLocalizedString("trial_period", comment: ""))
                                            .font(.subheadline.bold())
                                            .foregroundColor(.white)
                                        
                                        Text("\(variantManager.trialDurationDays) " + NSLocalizedString("days_completely_free", comment: ""))
                                            .font(.caption)
                                            .foregroundColor(.green)
                                    }
                                    
                                    Spacer()
                                    
                                    Text(NSLocalizedString("free", comment: ""))
                                        .font(.headline.bold())
                                        .foregroundColor(.green)
                                }
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.green.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                        )
                                )
                                
                                // After trial
                                HStack {
                                    Image(systemName: "arrow.clockwise")
                                        .foregroundColor(.blue)
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(NSLocalizedString("after_trial", comment: ""))
                                            .font(.subheadline.bold())
                                            .foregroundColor(.white)
                                        
                                        Text(NSLocalizedString("monthly_subscription", comment: ""))
                                            .font(.caption)
                                            .foregroundColor(.white.opacity(0.8))
                                    }
                                    
                                    Spacer()
                                    
                                    Text(subscription.displayPrice + NSLocalizedString("per_month", comment: ""))
                                        .font(.headline.bold())
                                        .foregroundColor(.blue)
                                }
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.blue.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                        )
                                )
                                
                                // Cancellation info
                                HStack {
                                    Image(systemName: "xmark.circle")
                                        .foregroundColor(.orange)
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(NSLocalizedString("cancellation", comment: ""))
                                            .font(.subheadline.bold())
                                            .foregroundColor(.white)
                                        
                                        Text(NSLocalizedString("cancel_anytime_honest", comment: ""))
                                            .font(.caption)
                                            .foregroundColor(.orange)
                                    }
                                    
                                    Spacer()
                                    
                                    Text(NSLocalizedString("easy", comment: ""))
                                        .font(.headline.bold())
                                        .foregroundColor(.orange)
                                }
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.orange.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                                        )
                                )
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }
                    
                    // CTA Button
                    VStack(spacing: 15) {
                        Button(action: {
                            purchaseAction()
                        }) {
                            HStack {
                                if subscriptionManager.purchaseState == .purchasing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(subscriptionManager.purchaseState == .purchasing ? NSLocalizedString("processing", comment: "") : "🤝 " + NSLocalizedString("try_honestly", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    colors: [Color.green, Color.blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 30))
                            .shadow(color: Color.green.opacity(0.3), radius: 10, x: 0, y: 5)
                        }
                        .disabled(subscriptionManager.purchaseState == .purchasing)
                        .scaleEffect(animateContent ? 1.0 : 0.9)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.8), value: animateContent)
                        
                        Text("🔒 " + NSLocalizedString("no_hidden_fees", comment: ""))
                            .font(.caption)
                            .foregroundColor(.green.opacity(0.8))
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                    
                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                        
                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            showStartTime = Date()
            animateContent = true
            Task {
                await subscriptionManager.loadProducts()
            }
            
            // Analytics
            AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
    }
    
    private func purchaseAction() {
        AnalyticsManager.shared.logPaywallPurchaseAttempt(variant: variantManager.getCurrentVariantForAnalytics())
        
        Task {
            await subscriptionManager.purchaseSubscription()
            if subscriptionManager.isSubscribed {
                subscriptionManager.markOfferAsShown()
                
                // Success analytics
                if let product = subscriptionManager.availableSubscriptions.first {
                    let revenue = Double(truncating: product.price as NSNumber)
                    AnalyticsManager.shared.logPaywallPurchaseSuccess(
                        variant: variantManager.getCurrentVariantForAnalytics(),
                        productId: product.id,
                        revenue: revenue
                    )
                }
                
                AnalyticsManager.shared.logPaywallTrialStarted(variant: variantManager.getCurrentVariantForAnalytics())
                dismiss()
            }
        }
    }
    
    private func logDismissal() {
        let timeSpent = Date().timeIntervalSince(showStartTime)
        AnalyticsManager.shared.logPaywallDismissed(
            variant: variantManager.getCurrentVariantForAnalytics(),
            timeSpent: timeSpent
        )
    }
}

struct HonestFactCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(color.opacity(0.2))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct BenefitRow: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 30, height: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
            
            Image(systemName: "checkmark.circle.fill")
                .font(.title3)
                .foregroundColor(.green)
        }
    }
}

#Preview {
    HonestPaywallView()
}
