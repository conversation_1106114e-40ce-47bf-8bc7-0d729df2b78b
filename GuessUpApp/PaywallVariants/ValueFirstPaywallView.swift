//
//  ValueFirstPaywallView.swift
//  GuessUpApp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 21.03.2025.
//

import SwiftUI
import StoreKit

struct ValueFirstPaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var variantManager = PaywallVariantManager.shared
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    @State private var animateContent = false
    @State private var showStartTime = Date()
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.3),
                    Color(red: 0.2, green: 0.1, blue: 0.4),
                    Color(red: 0.3, green: 0.2, blue: 0.5)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 0) {
                    // Header section
                    VStack(spacing: 20) {
                        // Close button
                        HStack {
                            Spacer()
                            Button(action: { 
                                logDismissal()
                                dismiss() 
                            }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .foregroundColor(.white.opacity(0.8))
                                    .padding()
                                    .background(Color.white.opacity(0.1))
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.horizontal)
                        
                        // Value proposition header
                        VStack(spacing: 16) {
                            Text("💰")
                                .font(.system(size: 60))
                                .scaleEffect(animateContent ? 1.0 : 0.8)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateContent)
                            
                            Text(NSLocalizedString("incredible_value", comment: ""))
                                .font(.largeTitle.bold())
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.2), value: animateContent)
                            
                            Text(NSLocalizedString("value_first_subtitle", comment: ""))
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .opacity(animateContent ? 1 : 0)
                                .animation(.easeInOut(duration: 0.8).delay(0.3), value: animateContent)
                        }
                        .padding(.horizontal)
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 30)
                    
                    // Value comparison section
                    VStack(spacing: 20) {
                        Text("📊 " + NSLocalizedString("compare_value", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        VStack(spacing: 16) {
                            // Free vs Premium comparison
                            ComparisonRow(
                                feature: NSLocalizedString("categories_access", comment: ""),
                                freeValue: "3 " + NSLocalizedString("basic_categories", comment: ""),
                                premiumValue: "15+ " + NSLocalizedString("premium_categories", comment: ""),
                                isHighlight: true
                            )
                            
                            ComparisonRow(
                                feature: NSLocalizedString("ads_experience", comment: ""),
                                freeValue: NSLocalizedString("with_ads", comment: ""),
                                premiumValue: NSLocalizedString("ad_free", comment: ""),
                                isHighlight: false
                            )
                            
                            ComparisonRow(
                                feature: NSLocalizedString("game_modes", comment: ""),
                                freeValue: NSLocalizedString("basic_mode", comment: ""),
                                premiumValue: NSLocalizedString("all_modes", comment: ""),
                                isHighlight: false
                            )
                            
                            ComparisonRow(
                                feature: NSLocalizedString("updates", comment: ""),
                                freeValue: NSLocalizedString("limited_updates", comment: ""),
                                premiumValue: NSLocalizedString("priority_updates", comment: ""),
                                isHighlight: false
                            )
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Cost breakdown
                    VStack(spacing: 20) {
                        Text("💡 " + NSLocalizedString("cost_breakdown", comment: ""))
                            .font(.headline.bold())
                            .foregroundColor(.white)
                        
                        VStack(spacing: 12) {
                            CostComparisonCard(
                                title: NSLocalizedString("coffee_comparison", comment: ""),
                                description: NSLocalizedString("less_than_coffee", comment: ""),
                                icon: "cup.and.saucer.fill",
                                color: .brown
                            )
                            
                            CostComparisonCard(
                                title: NSLocalizedString("entertainment_value", comment: ""),
                                description: NSLocalizedString("hours_of_fun", comment: ""),
                                icon: "gamecontroller.fill",
                                color: .purple
                            )
                            
                            CostComparisonCard(
                                title: NSLocalizedString("family_time", comment: ""),
                                description: NSLocalizedString("priceless_moments", comment: ""),
                                icon: "heart.fill",
                                color: .red
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                    
                    // Pricing section with savings highlight
                    if let subscription = subscriptionManager.availableSubscriptions.first {
                        VStack(spacing: 20) {
                            // Savings banner
                            HStack {
                                Image(systemName: "tag.fill")
                                    .foregroundColor(.green)
                                
                                Text("🎉 \(variantManager.discountPercentage)% " + NSLocalizedString("savings_with_trial", comment: ""))
                                    .font(.headline.bold())
                                    .foregroundColor(.green)
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.green.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.green.opacity(0.5), lineWidth: 1)
                                    )
                            )
                            
                            VStack(spacing: 8) {
                                Text("\(variantManager.trialDurationDays) " + NSLocalizedString("days_free", comment: ""))
                                    .font(.title.bold())
                                    .foregroundColor(.yellow)
                                
                                Text("Then \(subscription.displayPrice)\(NSLocalizedString("per_month", comment: ""))")
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                                
                                Text(NSLocalizedString("cancel_anytime", comment: ""))
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.white.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }
                    
                    // CTA Button
                    VStack(spacing: 15) {
                        Button(action: {
                            purchaseAction()
                        }) {
                            HStack {
                                if subscriptionManager.purchaseState == .purchasing {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(subscriptionManager.purchaseState == .purchasing ? NSLocalizedString("processing", comment: "") : "💎 " + NSLocalizedString("get_premium_value", comment: ""))
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(
                                LinearGradient(
                                    colors: [Color.green, Color.blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 30))
                            .shadow(color: Color.green.opacity(0.3), radius: 10, x: 0, y: 5)
                        }
                        .disabled(subscriptionManager.purchaseState == .purchasing)
                        .scaleEffect(animateContent ? 1.0 : 0.9)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.8), value: animateContent)
                        
                        Text("💰 " + NSLocalizedString("best_value_guarantee", comment: ""))
                            .font(.caption)
                            .foregroundColor(.yellow.opacity(0.8))
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                    
                    // Terms and Privacy
                    HStack(spacing: 20) {
                        Button(NSLocalizedString("terms_of_use", comment: "")) {
                            showingTerms = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                        
                        Button(NSLocalizedString("privacy_policy", comment: "")) {
                            showingPrivacy = true
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .underline()
                    }
                    .padding(.bottom, 40)
                }
            }
        }
        .onAppear {
            showStartTime = Date()
            animateContent = true
            Task {
                await subscriptionManager.loadProducts()
            }
            
            // Analytics
            AnalyticsManager.shared.logPaywallShown(variant: variantManager.getCurrentVariantForAnalytics())
        }
        .sheet(isPresented: $showingTerms) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/terms.html")!)
        }
        .sheet(isPresented: $showingPrivacy) {
            SafariView(url: URL(string: "https://planty-392c5.web.app/privacy.html")!)
        }
    }
    
    private func purchaseAction() {
        AnalyticsManager.shared.logPaywallPurchaseAttempt(variant: variantManager.getCurrentVariantForAnalytics())
        
        Task {
            await subscriptionManager.purchaseSubscription()
            if subscriptionManager.isSubscribed {
                subscriptionManager.markOfferAsShown()
                
                // Success analytics
                if let product = subscriptionManager.availableSubscriptions.first {
                    let revenue = Double(truncating: product.price as NSNumber)
                    AnalyticsManager.shared.logPaywallPurchaseSuccess(
                        variant: variantManager.getCurrentVariantForAnalytics(),
                        productId: product.id,
                        revenue: revenue
                    )
                }
                
                AnalyticsManager.shared.logPaywallTrialStarted(variant: variantManager.getCurrentVariantForAnalytics())
                dismiss()
            }
        }
    }
    
    private func logDismissal() {
        let timeSpent = Date().timeIntervalSince(showStartTime)
        AnalyticsManager.shared.logPaywallDismissed(
            variant: variantManager.getCurrentVariantForAnalytics(),
            timeSpent: timeSpent
        )
    }
}

struct ComparisonRow: View {
    let feature: String
    let freeValue: String
    let premiumValue: String
    let isHighlight: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(feature)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                HStack {
                    Text("Free:")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Text(freeValue)
                        .font(.caption)
                        .foregroundColor(.red.opacity(0.8))
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Premium")
                    .font(.subheadline.bold())
                    .foregroundColor(.yellow)
                
                Text(premiumValue)
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
        .padding(.vertical, 8)
        .background(
            isHighlight ? 
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.yellow.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                ) : nil
        )
    }
}

struct CostComparisonCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(color.opacity(0.2))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

#Preview {
    ValueFirstPaywallView()
}
